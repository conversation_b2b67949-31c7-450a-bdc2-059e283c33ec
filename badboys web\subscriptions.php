<?php
$pageTitle = "IPTV Subscription Plans";
$pageDescription = "Choose from our premium IPTV subscription plans. Basic, Pro, and Premium options with up to 4 simultaneous streams, 16,000+ channels and 40,000+ VOD content.";

require_once 'includes/header.php';
?>

    <!-- Hero Section -->
    <section class="subscriptions-hero py-5">
        <div class="container">
            <div class="row text-center">
                <div class="col-12">
                    <h1 class="hero-title">Choose Your IPTV Plan</h1>
                    <p class="hero-subtitle">All plans include 16,000+ live channels, 40,000+ VOD content, and 24/7 support</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Plans -->
    <section class="pricing-section py-5">
        <div class="container">
            <div class="row justify-content-center">
                <?php foreach ($subscription_plans as $planKey => $plan): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="pricing-card <?php echo isset($plan['popular']) && $plan['popular'] ? 'popular' : ''; ?>">
                        <?php if (isset($plan['popular']) && $plan['popular']): ?>
                        <div class="popular-badge">Most Popular</div>
                        <?php endif; ?>
                        <div class="pricing-header">
                            <h3><?php echo htmlspecialchars($plan['name']); ?></h3>
                            <div class="connections-badge"><?php echo $plan['connections']; ?> Streams</div>
                            <div class="price">
                                <span class="currency">$</span>
                                <span class="amount"><?php echo $plan['price']; ?></span>
                            </div>
                            <p class="price-period">per month</p>
                        </div>
                        <ul class="pricing-features">
                            <?php foreach ($plan['features'] as $feature): ?>
                            <li><i class="fas fa-check"></i> <?php echo htmlspecialchars($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <a href="checkout.php?plan=<?php echo $planKey; ?>&connections=<?php echo $plan['connections']; ?>&price=<?php echo $plan['price']; ?>" class="btn btn-primary btn-block">Subscribe Now</a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Custom Plans -->
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <div class="custom-plans-section">
                        <h4>Need More Than 4 Streams?</h4>
                        <p class="text-muted">We offer custom plans with up to 12 simultaneous streams for larger households and businesses.</p>
                        <a href="contact.php" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-envelope"></i> Contact Us for Custom Plans
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Comparison -->
    <section class="features-comparison py-5 bg-light">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">Plan Comparison</h2>
                    <p class="section-subtitle">Compare features across all our subscription plans</p>
                </div>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="comparison-table">
                        <table class="table table-bordered bg-white">
                            <thead>
                                <tr>
                                    <th>Features</th>
                                    <th>Basic Plan</th>
                                    <th>Pro Plan</th>
                                    <th>Premium Plan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Live TV Channels</strong></td>
                                    <td>16,000+</td>
                                    <td>16,000+</td>
                                    <td>16,000+</td>
                                </tr>
                                <tr>
                                    <td><strong>VOD & TV Shows</strong></td>
                                    <td>40,000+</td>
                                    <td>40,000+</td>
                                    <td>40,000+</td>
                                </tr>
                                <tr>
                                    <td><strong>Simultaneous Streams</strong></td>
                                    <td>2 Connections</td>
                                    <td>3 Connections</td>
                                    <td>4 Connections</td>
                                </tr>
                                <tr>
                                    <td><strong>Video Quality</strong></td>
                                    <td>4K/FHD/HD/SD</td>
                                    <td>4K/FHD/HD/SD</td>
                                    <td>4K/FHD/HD/SD</td>
                                </tr>
                                <tr>
                                    <td><strong>EPG Support</strong></td>
                                    <td><i class="fas fa-check text-success"></i></td>
                                    <td><i class="fas fa-check text-success"></i></td>
                                    <td><i class="fas fa-check text-success"></i></td>
                                </tr>
                                <tr>
                                    <td><strong>Catch-up TV</strong></td>
                                    <td><i class="fas fa-check text-success"></i></td>
                                    <td><i class="fas fa-check text-success"></i></td>
                                    <td><i class="fas fa-check text-success"></i></td>
                                </tr>
                                <tr>
                                    <td><strong>Support Level</strong></td>
                                    <td>24/7 Standard</td>
                                    <td>24/7 Standard</td>
                                    <td>24/7 Priority</td>
                                </tr>
                                <tr>
                                    <td><strong>Monthly Price</strong></td>
                                    <td><strong>$25</strong></td>
                                    <td><strong>$30</strong></td>
                                    <td><strong>$35</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">Frequently Asked Questions</h2>
                </div>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="faq-item">
                        <div class="faq-question">
                            <h5>Can I change my plan later?</h5>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Yes, you can upgrade or downgrade your plan at any time. Changes will take effect on your next billing cycle.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question">
                            <h5>What payment methods do you accept?</h5>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>We accept PayPal, credit cards (Visa, MasterCard, American Express), and cryptocurrency payments.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question">
                            <h5>Is there a contract or commitment?</h5>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>No, all our plans are month-to-month with no long-term contracts. You can cancel anytime.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question">
                            <h5>Do you offer refunds?</h5>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Yes, we offer a 7-day money-back guarantee. If you're not satisfied, contact us for a full refund.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
.subscriptions-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.pricing-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    position: relative;
    height: 100%;
    transition: transform 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-5px);
}

.pricing-card.popular {
    border: 3px solid #007bff;
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #007bff;
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.connections-badge {
    background: #28a745;
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9rem;
    margin-bottom: 20px;
    display: inline-block;
}

.price {
    margin: 20px 0;
}

.currency {
    font-size: 1.5rem;
    vertical-align: top;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: #007bff;
}

.price-period {
    color: #666;
    margin-bottom: 30px;
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin: 30px 0;
}

.pricing-features li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.pricing-features li:last-child {
    border-bottom: none;
}

.pricing-features i {
    color: #28a745;
    margin-right: 10px;
}

.btn-block {
    width: 100%;
    padding: 12px;
    font-weight: 600;
}

.custom-plans-section {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.comparison-table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.comparison-table table {
    margin: 0;
}

.comparison-table th {
    background: #007bff;
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 15px;
}

.comparison-table td {
    text-align: center;
    padding: 15px;
    vertical-align: middle;
}

.faq-item {
    background: white;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.faq-question {
    padding: 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: #e9ecef;
}

.faq-question h5 {
    margin: 0;
    flex: 1;
}

.faq-question i {
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 20px;
    max-height: 200px;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .pricing-card.popular {
        transform: none;
        margin-bottom: 30px;
    }
    
    .comparison-table {
        font-size: 0.9rem;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
