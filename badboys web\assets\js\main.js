// BadBoyz IPTV - Enhanced Main JavaScript File

// Global configuration
window.BadBoyzIPTV = {
    config: {
        animationDuration: 300,
        notificationDuration: 5000,
        scrollOffset: 80
    }
};

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initNavbar();
    initScrollEffects();
    initCounters();
    initContactForm();
    initPricingCards();
    initConnectionTabs();
    initStreamingChannels();
    initFAQ();
    initializeHeaderScrolling();
    initNotifications();
    initFormValidation();
});

// Navbar functionality
function initNavbar() {
    const navbar = document.querySelector('.navbar');
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Mobile menu toggle
    if (navbarToggler) {
        navbarToggler.addEventListener('click', function() {
            navbarCollapse.classList.toggle('show');
        });
    }

    // Close mobile menu when clicking on links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth < 992) {
                navbarCollapse.classList.remove('show');
            }
        });
    });
}

// Scroll effects and animations
function initScrollEffects() {
    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Fade in animation on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.pricing-card, .feature-card, .stats-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// Counter animation
function initCounters() {
    const counters = document.querySelectorAll('.counter');
    
    const countUp = (element, target) => {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            element.textContent = Math.floor(current);
            if (current >= target) {
                element.textContent = target;
                clearInterval(timer);
            }
        }, 20);
    };

    const counterObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = parseInt(entry.target.getAttribute('data-target'));
                countUp(entry.target, target);
                counterObserver.unobserve(entry.target);
            }
        });
    });

    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// Contact form handling
function initContactForm() {
    const contactForm = document.getElementById('contactForm');
    const trialForm = document.getElementById('trialForm');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleFormSubmission(this, 'contact');
        });
    }

    if (trialForm) {
        trialForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleFormSubmission(this, 'trial');
        });
    }
}

// Form submission handler
function handleFormSubmission(form, type) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    // Show loading state
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;

    // Simulate form submission (replace with actual API call)
    setTimeout(() => {
        // Reset form
        form.reset();
        
        // Show success message
        showNotification('Success! Your request has been submitted.', 'success');
        
        // Reset button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

// Pricing cards interaction
function initPricingCards() {
    const pricingCards = document.querySelectorAll('.pricing-card');

    pricingCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('popular')) {
                this.style.transform = 'translateY(0) scale(1)';
            }
        });
    });
}

// Connection tabs functionality
function initConnectionTabs() {
    const connectionTabs = document.querySelectorAll('.connection-tab');
    const pricingGrids = document.querySelectorAll('.pricing-grid');

    connectionTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const connections = this.dataset.connections;

            // Update active tab
            connectionTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Show corresponding pricing grid
            pricingGrids.forEach(grid => {
                grid.style.display = 'none';
            });

            const targetGrid = document.getElementById(`pricing-${connections}-connections`);
            if (targetGrid) {
                targetGrid.style.display = 'block';

                // Add animation
                targetGrid.style.opacity = '0';
                targetGrid.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    targetGrid.style.transition = 'all 0.5s ease';
                    targetGrid.style.opacity = '1';
                    targetGrid.style.transform = 'translateY(0)';
                }, 50);
            }
        });
    });
}

// FAQ functionality
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        
        if (question && answer) {
            question.addEventListener('click', function() {
                const isActive = item.classList.contains('active');
                
                // Close all FAQ items
                faqItems.forEach(faq => {
                    faq.classList.remove('active');
                    faq.querySelector('.faq-answer').style.maxHeight = '0';
                });
                
                // Open clicked item if it wasn't active
                if (!isActive) {
                    item.classList.add('active');
                    answer.style.maxHeight = answer.scrollHeight + 'px';
                }
            });
        }
    });
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Utility functions
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function formatPrice(price) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .fade-in {
        animation: fadeInUp 0.6s ease forwards;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .navbar.scrolled {
        background: rgba(255, 255, 255, 0.98) !important;
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    }

    .navbar.scrolled .navbar-brand,
    .navbar.scrolled .nav-link {
        color: #333 !important;
    }
`;
document.head.appendChild(style);

// Streaming channels animation and interaction
function initStreamingChannels() {
    const channelBubbles = document.querySelectorAll('.channel-bubble');

    if (channelBubbles.length === 0) return;

    // Add staggered animation delays
    channelBubbles.forEach((bubble, index) => {
        const delay = parseFloat(bubble.dataset.delay) || index * 0.5;
        bubble.style.animationDelay = `${delay}s`;

        // Add hover effects
        bubble.addEventListener('mouseenter', function() {
            this.style.animationPlayState = 'paused';
            this.style.transform = 'scale(1.2) translateY(-10px)';
            this.style.zIndex = '100';
        });

        bubble.addEventListener('mouseleave', function() {
            this.style.animationPlayState = 'running';
            this.style.transform = 'scale(1)';
            this.style.zIndex = '1';
        });

        // Add click effects
        bubble.addEventListener('click', function() {
            const channelName = this.querySelector('.channel-name').textContent;
            showChannelInfo(channelName);
        });
    });

    // Random movement for more dynamic effect
    setInterval(() => {
        channelBubbles.forEach(bubble => {
            if (!bubble.matches(':hover')) {
                const randomX = (Math.random() - 0.5) * 20;
                const randomY = (Math.random() - 0.5) * 20;
                bubble.style.transform += ` translate(${randomX}px, ${randomY}px)`;

                setTimeout(() => {
                    bubble.style.transform = bubble.style.transform.replace(/translate\([^)]*\)/g, '');
                }, 2000);
            }
        });
    }, 5000);
}

// Show channel information
function showChannelInfo(channelName) {
    const channelInfo = {
        'NETFLIX': 'Stream thousands of movies, TV shows, and Netflix Originals',
        'hulu': 'Watch current TV episodes, classic series, and Hulu Originals',
        'Disney+': 'Enjoy Disney, Pixar, Marvel, Star Wars, and National Geographic content',
        'HBO MAX': 'Premium entertainment with HBO series, movies, and Max Originals',
        'Prime Video': 'Amazon Prime Video with exclusive shows and movies',
        'Apple TV+': 'Apple\'s premium streaming service with original content',
        'Paramount+': 'CBS, Paramount Pictures, and exclusive Paramount+ content',
        'peacock': 'NBCUniversal content including live TV and exclusive shows',
        'Nickelodeon': 'Kids\' favorite cartoons and Nickelodeon shows',
        'Nick Jr.': 'Educational and entertaining content for preschoolers',
        'Cartoon Network': 'Animated series and cartoons for kids and teens',
        'discovery+': 'Real-life entertainment from Discovery networks',
        'ESPN': 'Live sports, highlights, and sports programming',
        'FOX': 'FOX network programming and live TV',
        'CNN': 'Breaking news and current events coverage',
        'BBC': 'British Broadcasting Corporation content and news'
    };

    const info = channelInfo[channelName] || 'Premium streaming content available';

    // Create and show notification
    showChannelNotification(channelName, info);
}

// Show channel notification
function showChannelNotification(channelName, info) {
    // Remove existing notification
    const existingNotification = document.querySelector('.channel-notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // Create notification
    const notification = document.createElement('div');
    notification.className = 'channel-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <h4>${channelName}</h4>
            <p>${info}</p>
            <small>Available with BadBoyz IPTV subscription</small>
        </div>
        <button class="close-notification" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Header scrolling effect
function initializeHeaderScrolling() {
    const header = document.querySelector('.header');
    if (!header) return;

    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Always keep header white when scrolled
        if (scrollTop > 50) {
            header.classList.add('scrolled');
            header.style.background = 'rgba(255,255,255,1)';
        } else {
            header.classList.remove('scrolled');
            header.style.background = 'rgba(255,255,255,0.98)';
        }

        // Hide header when scrolling down fast, show when scrolling up
        if (scrollTop > lastScrollTop && scrollTop > 200) {
            header.style.transform = 'translateY(-100%)';
        } else {
            header.style.transform = 'translateY(0)';
        }

        lastScrollTop = scrollTop;
    });
}

// Enhanced notification system
function initNotifications() {
    window.showNotification = function(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show notification-popup`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            min-width: 300px;
            max-width: 500px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Auto remove
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }
        }, duration);
    };
}

// Enhanced form validation
function initFormValidation() {
    // Real-time validation for all forms
    document.querySelectorAll('input[required], textarea[required], select[required]').forEach(field => {
        field.addEventListener('blur', function() {
            validateField(this);
        });

        field.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateField(this);
            }
        });
    });

    // Form submission validation
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                if (window.showNotification) {
                    showNotification('Please fill in all required fields correctly.', 'danger');
                }
            }
        });
    });
}

function validateField(field) {
    const value = field.value.trim();
    field.classList.remove('is-invalid', 'is-valid');

    if (!value) {
        field.classList.add('is-invalid');
        return false;
    }

    // Email validation
    if (field.type === 'email' && !validateEmail(value)) {
        field.classList.add('is-invalid');
        return false;
    }

    // Phone validation
    if (field.type === 'tel' && value.length < 10) {
        field.classList.add('is-invalid');
        return false;
    }

    field.classList.add('is-valid');
    return true;
}

function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });

    return isValid;
}

function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// Loading state management
function addLoadingState(button) {
    const originalText = button.innerHTML;
    const originalDisabled = button.disabled;

    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    button.disabled = true;

    return function() {
        button.innerHTML = originalText;
        button.disabled = originalDisabled;
    };
}

// Enhanced AJAX helper
function makeAjaxRequest(url, data, callback, method = 'POST') {
    const xhr = new XMLHttpRequest();
    xhr.open(method, url, true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    callback(response);
                } catch (e) {
                    callback({success: false, message: 'Invalid response from server'});
                }
            } else {
                callback({success: false, message: 'Server error occurred'});
            }
        }
    };

    // Add CSRF token to data
    if (typeof data === 'string') {
        data += '&csrf_token=' + encodeURIComponent(window.CSRF_TOKEN || '');
    } else if (typeof data === 'object') {
        data.csrf_token = window.CSRF_TOKEN || '';
        data = Object.keys(data).map(key => key + '=' + encodeURIComponent(data[key])).join('&');
    }

    xhr.send(data);
}
