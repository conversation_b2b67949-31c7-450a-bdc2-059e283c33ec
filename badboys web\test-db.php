<?php
// Simple Database Connection Test
// Use this file to quickly test your database connection

echo "<h2>Database Connection Test</h2>";

// Check if config file exists
if (!file_exists('config/config.php')) {
    echo "<p style='color: red;'>❌ Config file not found. Please run setup-guide.php first.</p>";
    echo "<a href='setup-guide.php'>Go to Setup Guide</a>";
    exit;
}

// Load config
require_once 'config/config.php';

echo "<h3>Configuration Check:</h3>";
echo "<p><strong>DB Host:</strong> " . DB_HOST . "</p>";
echo "<p><strong>DB Name:</strong> " . DB_NAME . "</p>";
echo "<p><strong>DB User:</strong> " . DB_USER . "</p>";
echo "<p><strong>DB Password:</strong> " . (DB_PASS ? '[SET]' : '[EMPTY]') . "</p>";

echo "<h3>Connection Test:</h3>";

try {
    // Test basic connection
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "<p style='color: green;'>✅ Basic MySQL connection successful</p>";
    
    // Test database connection
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Test if tables exist
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p style='color: orange;'>⚠️ Database is empty. Tables will be created automatically.</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($tables) . " tables in database</p>";
        echo "<p><strong>Tables:</strong> " . implode(', ', $tables) . "</p>";
    }
    
    // Test full system
    require_once 'config/database.php';
    echo "<p style='color: green;'>✅ Database system initialized successfully</p>";
    
    echo "<h3>✅ All Tests Passed!</h3>";
    echo "<p>Your database is configured correctly. You can now use your website.</p>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Website</a>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    
    echo "<h3>Common Solutions:</h3>";
    echo "<ul>";
    echo "<li>Check if your database credentials are correct</li>";
    echo "<li>Make sure your database server is running</li>";
    echo "<li>Verify the database name exists</li>";
    echo "<li>Check if your hosting provider uses a different host (not localhost)</li>";
    echo "<li>Contact your hosting provider for database details</li>";
    echo "</ul>";
    
    echo "<a href='setup-guide.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Run Setup Guide</a>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ System error: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f8f9fa;
}
h2, h3 {
    color: #333;
}
p {
    margin: 10px 0;
}
ul {
    background: white;
    padding: 20px;
    border-radius: 5px;
    border-left: 4px solid #ffc107;
}
</style>
