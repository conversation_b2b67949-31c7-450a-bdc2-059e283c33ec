<?php
$pageTitle = "Login";
$pageDescription = "Login to your BadBoyz IPTV account to manage your subscriptions and access your dashboard.";

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showAlert('Invalid security token. Please try again.', 'danger');
    } else {
        $email = sanitizeInput($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);
        
        if (empty($email) || empty($password)) {
            showAlert('Please fill in all fields.', 'danger');
        } elseif (!validateEmail($email)) {
            showAlert('Please enter a valid email address.', 'danger');
        } else {
            $result = loginUser($email, $password);
            
            if ($result['success']) {
                if ($remember) {
                    // Set remember me cookie (30 days)
                    setcookie('remember_token', bin2hex(random_bytes(32)), time() + (30 * 24 * 60 * 60), '/', '', true, true);
                }
                
                showAlert('Login successful! Welcome back.', 'success');
                
                // Redirect based on user type
                if (isAdmin()) {
                    redirect('admin/index.php');
                } else {
                    redirect('client-area.php');
                }
            } else {
                showAlert($result['message'], 'danger');
            }
        }
    }
}

require_once 'includes/header.php';
?>

<div class="auth-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="auth-card">
                    <div class="auth-header text-center mb-4">
                        <h2>Welcome Back</h2>
                        <p>Sign in to your <?php echo APP_NAME; ?> account</p>
                    </div>
                    
                    <form method="POST" class="auth-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="email" name="email" required 
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="password-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 d-flex justify-content-between align-items-center">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Remember me
                                </label>
                            </div>
                            <a href="forgot-password.php" class="text-decoration-none">Forgot Password?</a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-sign-in-alt"></i> Sign In
                        </button>
                    </form>
                    
                    <div class="auth-divider">
                        <span>or</span>
                    </div>
                    
                    <div class="text-center">
                        <p>Don't have an account? <a href="register.php" class="text-decoration-none">Create Account</a></p>
                    </div>
                    
                    <div class="auth-links text-center mt-4">
                        <div class="row">
                            <div class="col-6">
                                <a href="free-trial.php" class="btn btn-outline-success btn-sm w-100">
                                    <i class="fas fa-play"></i> Free Trial
                                </a>
                            </div>
                            <div class="col-6">
                                <a href="contact.php" class="btn btn-outline-info btn-sm w-100">
                                    <i class="fas fa-headset"></i> Support
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.auth-section {
    min-height: 80vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
}

.auth-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.auth-header h2 {
    color: #333;
    font-weight: 600;
}

.auth-header p {
    color: #666;
    margin-bottom: 0;
}

.input-group-text {
    background: #f8f9fa;
    border-right: none;
}

.form-control {
    border-left: none;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.auth-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
}

.auth-divider span {
    background: white;
    padding: 0 15px;
    color: #666;
    position: relative;
}

.auth-links .btn {
    font-size: 0.875rem;
}
</style>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

// Auto-focus first input
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('email').focus();
});
</script>

<?php require_once 'includes/footer.php'; ?>
