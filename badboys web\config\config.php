<?php
// BadBoyz IPTV - Main Configuration File
// Security: Prevent direct access
if (!defined('BADBOYZ_IPTV')) {
    die('Direct access not permitted');
}

// Define application constants
define('BADBOYZ_IPTV', true);
define('APP_NAME', 'BadBoyz IPTV');
define('APP_VERSION', '2.0.0');
define('APP_URL', 'https://badboyzmedia.org/');
define('ADMIN_EMAIL', '<EMAIL>');
define('SUPPORT_EMAIL', '<EMAIL>');

// Database Configuration - Your InMotion Database Details
define('DB_HOST', 'localhost');
define('DB_NAME', 'c0e0425_badboyz_iptv');
define('DB_USER', 'c0e0425_badboyz_user');
define('DB_PASS', 'MMo]F;K@3Jy*');
define('DB_CHARSET', 'utf8mb4');

// Security Configuration
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_LIFETIME', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Email Configuration (SMTP)
define('SMTP_HOST', 'smtp.your-hosting.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-email-password');
define('SMTP_ENCRYPTION', 'tls');

// Payment Configuration
define('PAYPAL_CLIENT_ID', 'AQzrjHPKR67wMzFhUuFDGMw3y_YUyiOv3GjF905UWbPtmfD1W1BKwRXNdccQPvl1e8lmi_YcVlCN8tRW');
define('PAYPAL_CLIENT_SECRET', 'your-paypal-client-secret');
define('PAYPAL_SANDBOX', true); // Set to false for production

// IPTV Server Configuration
$iptv_servers = [
    'main' => [
        'name' => 'Main Server',
        'url' => 'http://server.badboyz-iptv.com:8080',
        'location' => 'USA',
        'status' => 'active'
    ],
    'backup' => [
        'name' => 'Backup Server',
        'url' => 'http://backup.badboyz-iptv.com:8080',
        'location' => 'Europe',
        'status' => 'active'
    ]
];

// Subscription Plans
$subscription_plans = [
    'basic' => [
        'name' => 'Basic Plan',
        'price' => 25,
        'connections' => 2,
        'features' => [
            '16,000+ Live IPTV Channels',
            '40,000+ VOD & TV Shows',
            '2 Simultaneous Streams',
            '99.99% Uptime',
            '4K/FHD/HD/SD Quality',
            'US-Based Service',
            '24/7 Support'
        ]
    ],
    'pro' => [
        'name' => 'Pro Plan',
        'price' => 30,
        'connections' => 3,
        'features' => [
            '16,000+ Live IPTV Channels',
            '40,000+ VOD & TV Shows',
            '3 Simultaneous Streams',
            '99.99% Uptime',
            '4K/FHD/HD/SD Quality',
            'US-Based Service',
            '24/7 Support'
        ]
    ],
    'premium' => [
        'name' => 'Premium Plan',
        'price' => 35,
        'connections' => 4,
        'popular' => true,
        'features' => [
            '16,000+ Live IPTV Channels',
            '40,000+ VOD & TV Shows',
            '4 Simultaneous Streams',
            '99.99% Uptime',
            '4K/FHD/HD/SD Quality',
            'US-Based Service',
            'Priority Support'
        ]
    ]
];

// Site Settings
$site_settings = [
    'maintenance_mode' => false,
    'registration_enabled' => true,
    'trial_enabled' => true,
    'trial_duration' => 24, // hours
    'max_trials_per_ip' => 1,
    'reseller_program' => true,
    'live_chat_enabled' => true,
    'analytics_enabled' => true,
    'google_analytics_id' => 'G-XXXXXXXXXX',
    'facebook_pixel_id' => 'XXXXXXXXXX'
];

// Error Reporting (Disable in production)
if (defined('DEVELOPMENT') && DEVELOPMENT) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Timezone
date_default_timezone_set('America/New_York');

// Session Configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
