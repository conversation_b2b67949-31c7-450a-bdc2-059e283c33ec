// Live Chat Functionality for BadBoyz IPTV
// Advanced chat widget with real-time support and professional features

class LiveChat {
    constructor() {
        this.isOpen = false;
        this.isTyping = false;
        this.messageCount = 0;
        this.sessionId = null;
        this.visitorId = null;
        this.responses = this.initializeResponses();
        this.init();
    }

    init() {
        // Initialize chat session
        this.initializeChatSession();

        // Show notification badge after 5 seconds
        setTimeout(() => {
            this.showNotification();
        }, 5000);

        // Auto-open chat after 30 seconds if not opened
        setTimeout(() => {
            if (!this.isOpen && !localStorage.getItem('chatDismissed')) {
                this.openChat();
                this.addBotMessage("👋 Hi there! Need help with BadBoyz IPTV? I'm here to assist you!");
            }
        }, 30000);

        // Initialize chat input
        this.initializeChatInput();

        // Poll for new messages every 3 seconds
        setInterval(() => {
            if (this.sessionId && this.isOpen) {
                this.checkForNewMessages();
            }
        }, 3000);
    }

    async initializeChatSession() {
        try {
            const response = await fetch('api/livechat.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=start_session&page=${encodeURIComponent(window.location.pathname)}`
            });

            const data = await response.json();
            if (data.success) {
                this.sessionId = data.session_id;
                this.visitorId = data.visitor_id;
            }
        } catch (error) {
            console.log('Chat session initialization failed, using offline mode');
        }
    }

    async checkForNewMessages() {
        if (!this.sessionId) return;

        try {
            const response = await fetch(`api/livechat.php?action=get_messages&session_id=${this.sessionId}`);
            const data = await response.json();

            if (data.success && data.messages) {
                // Check for new admin messages
                const adminMessages = data.messages.filter(msg => msg.sender === 'admin');
                const lastAdminMessage = adminMessages[adminMessages.length - 1];

                if (lastAdminMessage && !this.hasDisplayedMessage(lastAdminMessage.id)) {
                    this.addAdminMessage(lastAdminMessage.message, lastAdminMessage.time_display);
                    this.markMessageAsDisplayed(lastAdminMessage.id);
                }
            }
        } catch (error) {
            console.log('Failed to check for new messages');
        }
    }

    initializeResponses() {
        return {
            greetings: [
                "Hello! How can I help you with BadBoyz IPTV today?",
                "Hi there! Welcome to BadBoyz IPTV. What can I assist you with?",
                "Hey! Thanks for visiting BadBoyz IPTV. How may I help you?"
            ],
            pricing: [
                "Our pricing starts at $15/month for 2 connections. We have plans for 2, 3, and up to 8 connections. Would you like to see our full pricing?",
                "We offer flexible pricing: 2 connections ($15-85), 3 connections ($20-110), and up to 8 connections ($35-200). Which plan interests you?",
                "Great question! Our plans range from $15 for 1 month with 2 connections to $200 for 12 months with 8 connections. What's your preference?"
            ],
            setup: [
                "I'd be happy to help with setup! What device are you using? We support Smart TVs, Android, iOS, Fire Stick, MAG boxes, and more.",
                "Setup is easy! First, tell me your device type and I'll guide you through the process step by step.",
                "No problem! Our setup process is simple. Which device would you like to configure?"
            ],
            trial: [
                "Yes! We offer a FREE 24-hour trial. You can request it on our Free Trial page. Would you like me to guide you there?",
                "Absolutely! Our free trial gives you full access for 24 hours. Just fill out the trial form and you'll get your credentials within minutes.",
                "We do offer a 24-hour free trial! It includes access to all 16,000+ channels and 40,000+ VOD content. Interested?"
            ],
            support: [
                "Our technical support team is available 24/7. You can reach us via live chat, email, or Telegram. What specific issue are you experiencing?",
                "I'm here to help! For technical issues, I can assist with basic troubleshooting or connect you with our technical team. What's the problem?",
                "Technical support is one of our strengths! We offer 24/7 assistance. What device or issue are you having trouble with?"
            ],
            channels: [
                "We offer 16,000+ live TV channels from around the world including USA, UK, Canada, Europe, Asia, and more. Plus 40,000+ movies and TV shows!",
                "Our channel lineup includes sports, news, entertainment, movies, and international content from over 50 countries. What type of content interests you most?",
                "With 16,000+ channels, we cover everything: sports (ESPN, Fox Sports), news (CNN, BBC), entertainment, and international channels. Want specifics?"
            ],
            devices: [
                "StreamPro IPTV works on all major devices: Smart TVs (Samsung, LG, Sony), Android devices, iOS, Fire Stick, MAG boxes, PC, and more!",
                "We support virtually every device! Smart TVs, Android boxes, Fire Stick, Apple TV, MAG boxes, smartphones, tablets, and computers. What's your device?",
                "Great question! Our service is compatible with Smart TVs, Android, iOS, Fire Stick, MAG boxes, Kodi, VLC, and many more. Very flexible!"
            ],
            payment: [
                "We accept PayPal, credit/debit cards, and cryptocurrencies (Bitcoin, Ethereum, USDT). All payments are secure and processed instantly.",
                "Payment is secure and easy! We accept PayPal, major credit cards, and crypto payments. Which method would you prefer?",
                "We offer multiple payment options: PayPal (most popular), credit cards, and cryptocurrency. All transactions are encrypted and secure."
            ],
            refund: [
                "Yes! We offer a 7-day money-back guarantee. If you're not satisfied with our service, contact us within 7 days for a full refund.",
                "Absolutely! We stand behind our service with a 7-day money-back guarantee. No questions asked if you're not completely satisfied.",
                "We offer a 7-day refund policy. If our service doesn't meet your expectations, just contact us within a week for a full refund."
            ],
            default: [
                "I understand you're asking about that. Let me connect you with a human agent who can provide more detailed assistance.",
                "That's a great question! For the most accurate information, I'd recommend speaking with one of our specialists. Shall I connect you?",
                "I want to make sure you get the best answer. Let me transfer you to one of our expert team members who can help you properly."
            ]
        };
    }

    initializeChatInput() {
        const chatInput = document.getElementById('chatInput');
        if (chatInput) {
            chatInput.addEventListener('focus', () => {
                this.hideNotification();
            });
        }
    }

    toggleChat() {
        if (this.isOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }

    openChat() {
        const chatWindow = document.getElementById('chatWindow');
        const chatButton = document.getElementById('chatButton');
        
        if (chatWindow && chatButton) {
            chatWindow.style.display = 'flex';
            chatButton.innerHTML = '<i class="fas fa-times"></i>';
            this.isOpen = true;
            this.hideNotification();
            
            // Focus on input
            setTimeout(() => {
                const chatInput = document.getElementById('chatInput');
                if (chatInput) chatInput.focus();
            }, 300);

            // Mark as opened
            localStorage.setItem('chatOpened', 'true');
        }
    }

    closeChat() {
        const chatWindow = document.getElementById('chatWindow');
        const chatButton = document.getElementById('chatButton');
        
        if (chatWindow && chatButton) {
            chatWindow.style.display = 'none';
            chatButton.innerHTML = '<i class="fas fa-comments"></i>';
            this.isOpen = false;
            
            // Mark as dismissed
            localStorage.setItem('chatDismissed', 'true');
        }
    }

    showNotification() {
        const badge = document.getElementById('notificationBadge');
        if (badge && !this.isOpen) {
            badge.style.display = 'flex';
        }
    }

    hideNotification() {
        const badge = document.getElementById('notificationBadge');
        if (badge) {
            badge.style.display = 'none';
        }
    }

    addMessage(message, isUser = false) {
        const chatBody = document.getElementById('chatBody');
        if (!chatBody) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${isUser ? 'user' : 'bot'}`;
        
        if (isUser) {
            messageDiv.innerHTML = `<p>${message}</p>`;
        } else {
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div>
                    <p>${message}</p>
                </div>
            `;
        }

        chatBody.appendChild(messageDiv);
        this.scrollToBottom();
        this.messageCount++;
    }

    addBotMessage(message) {
        this.addMessage(message, false);
    }

    addUserMessage(message) {
        this.addMessage(message, true);
    }

    showTypingIndicator() {
        if (this.isTyping) return;
        
        const chatBody = document.getElementById('chatBody');
        if (!chatBody) return;

        this.isTyping = true;
        const typingDiv = document.createElement('div');
        typingDiv.className = 'typing-indicator';
        typingDiv.id = 'typingIndicator';
        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        `;

        chatBody.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
            this.isTyping = false;
        }
    }

    scrollToBottom() {
        const chatBody = document.getElementById('chatBody');
        if (chatBody) {
            chatBody.scrollTop = chatBody.scrollHeight;
        }
    }

    getResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        // Keyword matching for responses
        if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('plan')) {
            return this.getRandomResponse('pricing');
        } else if (lowerMessage.includes('setup') || lowerMessage.includes('install') || lowerMessage.includes('configure')) {
            return this.getRandomResponse('setup');
        } else if (lowerMessage.includes('trial') || lowerMessage.includes('free') || lowerMessage.includes('test')) {
            return this.getRandomResponse('trial');
        } else if (lowerMessage.includes('support') || lowerMessage.includes('help') || lowerMessage.includes('problem')) {
            return this.getRandomResponse('support');
        } else if (lowerMessage.includes('channel') || lowerMessage.includes('content') || lowerMessage.includes('tv')) {
            return this.getRandomResponse('channels');
        } else if (lowerMessage.includes('device') || lowerMessage.includes('compatible') || lowerMessage.includes('work')) {
            return this.getRandomResponse('devices');
        } else if (lowerMessage.includes('payment') || lowerMessage.includes('pay') || lowerMessage.includes('credit')) {
            return this.getRandomResponse('payment');
        } else if (lowerMessage.includes('refund') || lowerMessage.includes('money back') || lowerMessage.includes('guarantee')) {
            return this.getRandomResponse('refund');
        } else if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
            return this.getRandomResponse('greetings');
        } else {
            return this.getRandomResponse('default');
        }
    }

    getRandomResponse(category) {
        const responses = this.responses[category] || this.responses.default;
        return responses[Math.floor(Math.random() * responses.length)];
    }

    async sendMessage() {
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');

        if (!chatInput || !sendButton) return;

        const message = chatInput.value.trim();
        if (!message) return;

        // Add user message
        this.addUserMessage(message);
        chatInput.value = '';

        // Disable send button temporarily
        sendButton.disabled = true;

        // Send message to backend if session exists
        if (this.sessionId) {
            try {
                await fetch('api/livechat.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=send_message&session_id=${this.sessionId}&message=${encodeURIComponent(message)}&sender=customer`
                });

                // Show typing indicator for admin response
                this.showTypingIndicator();

                // Wait for potential admin response (or show auto-response after delay)
                setTimeout(() => {
                    this.hideTypingIndicator();
                    if (!this.hasRecentAdminMessage()) {
                        const response = this.getResponse(message);
                        this.addBotMessage(response);
                    }
                    sendButton.disabled = false;
                    chatInput.focus();
                }, 3000);

            } catch (error) {
                // Fallback to offline mode
                this.showTypingIndicator();
                setTimeout(() => {
                    this.hideTypingIndicator();
                    const response = this.getResponse(message);
                    this.addBotMessage(response);
                    sendButton.disabled = false;
                    chatInput.focus();
                }, 1000 + Math.random() * 2000);
            }
        } else {
            // Offline mode
            this.showTypingIndicator();
            setTimeout(() => {
                this.hideTypingIndicator();
                const response = this.getResponse(message);
                this.addBotMessage(response);
                sendButton.disabled = false;
                chatInput.focus();
            }, 1000 + Math.random() * 2000);
        }
    }

    hasDisplayedMessage(messageId) {
        const displayedMessages = JSON.parse(localStorage.getItem('displayedMessages') || '[]');
        return displayedMessages.includes(messageId);
    }

    markMessageAsDisplayed(messageId) {
        const displayedMessages = JSON.parse(localStorage.getItem('displayedMessages') || '[]');
        displayedMessages.push(messageId);
        localStorage.setItem('displayedMessages', JSON.stringify(displayedMessages));
    }

    hasRecentAdminMessage() {
        // Check if we received an admin message in the last 5 seconds
        const lastAdminMessageTime = localStorage.getItem('lastAdminMessageTime');
        if (lastAdminMessageTime) {
            const timeDiff = Date.now() - parseInt(lastAdminMessageTime);
            return timeDiff < 5000; // 5 seconds
        }
        return false;
    }

    addAdminMessage(message, timeDisplay) {
        const chatBody = document.getElementById('chatBody');
        if (!chatBody) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message bot';
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-headset"></i>
            </div>
            <div>
                <p>${message}</p>
                <small class="message-time">${timeDisplay}</small>
            </div>
        `;

        chatBody.appendChild(messageDiv);
        this.scrollToBottom();

        // Store timestamp
        localStorage.setItem('lastAdminMessageTime', Date.now().toString());
    }

    sendQuickReply(message) {
        const chatInput = document.getElementById('chatInput');
        if (chatInput) {
            chatInput.value = message;
            this.sendMessage();
        }
    }

    handleKeyPress(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            this.sendMessage();
        }
    }
}

// Initialize live chat when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.liveChat = new LiveChat();
});

// Global functions for HTML onclick events
function toggleChat() {
    if (window.liveChat) {
        window.liveChat.toggleChat();
    }
}

function sendMessage() {
    if (window.liveChat) {
        window.liveChat.sendMessage();
    }
}

function sendQuickReply(message) {
    if (window.liveChat) {
        window.liveChat.sendQuickReply(message);
    }
}

function handleChatKeyPress(event) {
    if (window.liveChat) {
        window.liveChat.handleKeyPress(event);
    }
}
