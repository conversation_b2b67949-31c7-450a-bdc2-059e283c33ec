<?php
$pageTitle = "Create Account";
$pageDescription = "Create your BadBoyz IPTV account to start enjoying premium IPTV services with thousands of channels and VOD content.";

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showAlert('Invalid security token. Please try again.', 'danger');
    } else {
        $firstName = sanitizeInput($_POST['first_name'] ?? '');
        $lastName = sanitizeInput($_POST['last_name'] ?? '');
        $username = sanitizeInput($_POST['username'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $phone = sanitizeInput($_POST['phone'] ?? '');
        $country = sanitizeInput($_POST['country'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        $agreeTerms = isset($_POST['agree_terms']);
        
        // Validation
        $errors = [];
        
        if (empty($firstName)) $errors[] = 'First name is required';
        if (empty($lastName)) $errors[] = 'Last name is required';
        if (empty($username)) $errors[] = 'Username is required';
        if (empty($email)) $errors[] = 'Email is required';
        if (empty($password)) $errors[] = 'Password is required';
        if (empty($confirmPassword)) $errors[] = 'Password confirmation is required';
        if (!$agreeTerms) $errors[] = 'You must agree to the terms and conditions';
        
        if (!validateEmail($email)) {
            $errors[] = 'Please enter a valid email address';
        }
        
        if (strlen($username) < 3) {
            $errors[] = 'Username must be at least 3 characters long';
        }
        
        if (strlen($password) < 6) {
            $errors[] = 'Password must be at least 6 characters long';
        }
        
        if ($password !== $confirmPassword) {
            $errors[] = 'Passwords do not match';
        }
        
        // Check if username or email already exists
        if (empty($errors)) {
            $existingUser = fetchOne("SELECT id FROM users WHERE username = ? OR email = ?", [$username, $email]);
            if ($existingUser) {
                $errors[] = 'Username or email already exists';
            }
        }
        
        if (empty($errors)) {
            // Create user account
            $userData = [
                'username' => $username,
                'email' => $email,
                'password' => password_hash($password, PASSWORD_DEFAULT),
                'first_name' => $firstName,
                'last_name' => $lastName,
                'phone' => $phone,
                'country' => $country,
                'status' => 'active',
                'user_type' => 'customer'
            ];
            
            $userId = insertRecord('users', $userData);
            
            if ($userId) {
                // Send welcome email
                $subject = 'Welcome to ' . APP_NAME . '!';
                $message = "
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #007bff;'>Welcome to " . APP_NAME . "!</h2>
                        <p>Dear {$firstName},</p>
                        <p>Thank you for creating an account with us. Your account has been successfully created.</p>
                        <p><strong>Account Details:</strong></p>
                        <ul>
                            <li>Username: {$username}</li>
                            <li>Email: {$email}</li>
                        </ul>
                        <p>You can now:</p>
                        <ul>
                            <li>Browse our subscription plans</li>
                            <li>Request a free 24-hour trial</li>
                            <li>Access your client dashboard</li>
                            <li>Get 24/7 support</li>
                        </ul>
                        <p style='margin-top: 30px;'>
                            <a href='" . APP_URL . "/login.php' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;'>Login to Your Account</a>
                        </p>
                        <p>If you have any questions, feel free to contact our support team.</p>
                        <p>Best regards,<br>The " . APP_NAME . " Team</p>
                    </div>
                </body>
                </html>";
                
                sendEmail($email, $subject, $message);
                
                showAlert('Account created successfully! Please login to continue.', 'success');
                redirect('login.php');
            } else {
                showAlert('Failed to create account. Please try again.', 'danger');
            }
        } else {
            foreach ($errors as $error) {
                showAlert($error, 'danger');
                break; // Show only first error
            }
        }
    }
}

require_once 'includes/header.php';
?>

<div class="auth-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="auth-card">
                    <div class="auth-header text-center mb-4">
                        <h2>Create Account</h2>
                        <p>Join <?php echo APP_NAME; ?> and start streaming today</p>
                    </div>
                    
                    <form method="POST" class="auth-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required
                                       value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required
                                       value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="username" name="username" required
                                       value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                            </div>
                            <small class="form-text text-muted">At least 3 characters, letters and numbers only</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="email" name="email" required
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="country" class="form-label">Country</label>
                                <select class="form-control" id="country" name="country">
                                    <option value="">Select Country</option>
                                    <option value="US" <?php echo (isset($_POST['country']) && $_POST['country'] == 'US') ? 'selected' : ''; ?>>United States</option>
                                    <option value="CA" <?php echo (isset($_POST['country']) && $_POST['country'] == 'CA') ? 'selected' : ''; ?>>Canada</option>
                                    <option value="UK" <?php echo (isset($_POST['country']) && $_POST['country'] == 'UK') ? 'selected' : ''; ?>>United Kingdom</option>
                                    <option value="DE" <?php echo (isset($_POST['country']) && $_POST['country'] == 'DE') ? 'selected' : ''; ?>>Germany</option>
                                    <option value="FR" <?php echo (isset($_POST['country']) && $_POST['country'] == 'FR') ? 'selected' : ''; ?>>France</option>
                                    <option value="IT" <?php echo (isset($_POST['country']) && $_POST['country'] == 'IT') ? 'selected' : ''; ?>>Italy</option>
                                    <option value="ES" <?php echo (isset($_POST['country']) && $_POST['country'] == 'ES') ? 'selected' : ''; ?>>Spain</option>
                                    <option value="AU" <?php echo (isset($_POST['country']) && $_POST['country'] == 'AU') ? 'selected' : ''; ?>>Australia</option>
                                    <option value="OTHER">Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                        <i class="fas fa-eye" id="password-eye"></i>
                                    </button>
                                </div>
                                <small class="form-text text-muted">At least 6 characters</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye" id="confirm_password-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agree_terms" name="agree_terms" required>
                                <label class="form-check-label" for="agree_terms">
                                    I agree to the <a href="terms-of-service.php" target="_blank">Terms of Service</a> and <a href="privacy-policy.php" target="_blank">Privacy Policy</a>
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus"></i> Create Account
                        </button>
                    </form>
                    
                    <div class="auth-divider">
                        <span>or</span>
                    </div>
                    
                    <div class="text-center">
                        <p>Already have an account? <a href="login.php" class="text-decoration-none">Sign In</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.auth-section {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    padding: 40px 0;
}

.auth-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.auth-header h2 {
    color: #333;
    font-weight: 600;
}

.auth-header p {
    color: #666;
    margin-bottom: 0;
}

.input-group-text {
    background: #f8f9fa;
    border-right: none;
}

.form-control {
    border-left: none;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.auth-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
}

.auth-divider span {
    background: white;
    padding: 0 15px;
    color: #666;
    position: relative;
}
</style>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

// Password strength indicator
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = getPasswordStrength(password);
    // You can add visual password strength indicator here
});

function getPasswordStrength(password) {
    let strength = 0;
    if (password.length >= 6) strength++;
    if (password.match(/[a-z]/)) strength++;
    if (password.match(/[A-Z]/)) strength++;
    if (password.match(/[0-9]/)) strength++;
    if (password.match(/[^a-zA-Z0-9]/)) strength++;
    return strength;
}

// Auto-focus first input
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('first_name').focus();
});
</script>

<?php require_once 'includes/footer.php'; ?>
