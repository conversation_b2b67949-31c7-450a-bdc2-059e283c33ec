# BadBoyz IPTV - InMotion Hosting Setup Guide

## 🚀 Quick Setup for InMotion Hosting

### Step 1: Create Database in cPanel

1. **Login to your InMotion cPanel**
2. **Go to "MySQL Databases"** section
3. **Create a new database:**
   - Database Name: `badboyz_iptv` (it will become `yourusername_badboyz_iptv`)
   - Click "Create Database"

4. **Create a database user:**
   - Username: `badboyz_user` (it will become `yourusername_badboyz_user`)
   - Password: Create a strong password
   - Click "Create User"

5. **Add user to database:**
   - Select your database and user
   - Grant "ALL PRIVILEGES"
   - Click "Add"

### Step 2: Update Database Configuration

Edit the file `config/config.php` and update these lines:

```php
// Replace these with your actual details from cPanel
define('DB_HOST', 'localhost');
define('DB_NAME', 'yourusername_badboyz_iptv');    // Your actual database name
define('<PERSON>_<PERSON><PERSON>', 'yourusername_badboyz_user');    // Your actual database user
define('DB_PASS', 'your_actual_password');         // Your actual password
```

### Step 3: Update Site Settings

Also update these in `config/config.php`:

```php
define('APP_URL', 'https://yourdomain.com');       // Your actual domain
define('ADMIN_EMAIL', '<EMAIL>');     // Your admin email
define('SUPPORT_EMAIL', '<EMAIL>'); // Your support email
```

### Step 4: Test Your Website

1. Upload all files to your `public_html` folder
2. Visit your website: `https://yourdomain.com`
3. The database tables will be created automatically
4. Default admin login:
   - **Username:** admin
   - **Password:** admin123
   - **Login URL:** `https://yourdomain.com/admin/`

## 🔧 Common InMotion Database Issues & Solutions

### Issue: "Database connection failed"

**Solution 1: Check Database Details**
- Make sure database name includes your cPanel username prefix
- Example: If your cPanel username is `john123`, database name should be `john123_badboyz_iptv`

**Solution 2: Verify User Permissions**
- In cPanel > MySQL Databases
- Make sure your user has "ALL PRIVILEGES" on the database

**Solution 3: Check Password**
- Make sure you're using the correct database user password
- Try recreating the database user if needed

### Issue: "Access denied for user"

**Solution:**
- Double-check the database username format: `yourusername_dbuser`
- Verify the password is correct
- Make sure the user is added to the database with proper privileges

### Issue: "Unknown database"

**Solution:**
- Check if the database was created successfully in cPanel
- Verify the database name format: `yourusername_databasename`
- Make sure you're using the full database name with prefix

## 📋 InMotion-Specific Notes

1. **Database Names:** InMotion automatically adds your cPanel username as a prefix
2. **File Permissions:** Usually set correctly by default
3. **PHP Version:** Make sure you're using PHP 7.4 or higher
4. **SSL Certificate:** Enable free SSL in cPanel for HTTPS

## 🎯 Quick Test

Create a simple test file `test-connection.php`:

```php
<?php
// Test database connection
$host = 'localhost';
$dbname = 'yourusername_badboyz_iptv';  // Replace with your actual database name
$username = 'yourusername_badboyz_user'; // Replace with your actual username
$password = 'your_actual_password';      // Replace with your actual password

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    echo "✅ Database connection successful!";
} catch(PDOException $e) {
    echo "❌ Connection failed: " . $e->getMessage();
}
?>
```

Upload this file and visit it in your browser to test the connection.

## 🆘 Still Having Issues?

1. **Check cPanel Error Logs** (in cPanel > Error Logs)
2. **Contact InMotion Support** - they can help verify database setup
3. **Use phpMyAdmin** in cPanel to test database access directly

## ✅ After Setup is Complete

1. **Delete test files** (`test-connection.php`, `inmotion-setup.md`)
2. **Change admin password** immediately
3. **Configure email settings** for notifications
4. **Test all website features**

---

**Need Help?** Contact InMotion support - they're very helpful with database setup issues!
