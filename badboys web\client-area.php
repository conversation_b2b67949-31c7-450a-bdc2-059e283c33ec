<?php
$pageTitle = "Client Dashboard";
$pageDescription = "Manage your IPTV subscriptions, view account details, and access support from your personal dashboard.";

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// Require login
requireLogin();

// Get user data
$userId = $_SESSION['user_id'];
$user = fetchOne("SELECT * FROM users WHERE id = ?", [$userId]);

// Get user subscriptions
$subscriptions = fetchAll("SELECT * FROM subscriptions WHERE user_id = ? ORDER BY created_at DESC", [$userId]);

// Get user support tickets
$tickets = fetchAll("SELECT * FROM support_tickets WHERE user_id = ? ORDER BY created_at DESC LIMIT 5", [$userId]);

// Get user trials
$trials = fetchAll("SELECT * FROM trials WHERE email = ? ORDER <PERSON>Y created_at DESC LIMIT 3", [$user['email']]);

// Calculate stats
$activeSubscriptions = count(array_filter($subscriptions, function($sub) {
    return $sub['status'] === 'active';
}));

$totalSpent = array_sum(array_column($subscriptions, 'price'));

require_once 'includes/header.php';
?>

<div class="client-area-section py-5">
    <div class="container">
        <!-- Welcome Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="welcome-card">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1>Welcome back, <?php echo htmlspecialchars($user['first_name']); ?>!</h1>
                            <p class="mb-0">Manage your IPTV subscriptions and account settings from your dashboard.</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="user-avatar">
                                <i class="fas fa-user-circle fa-4x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-tv"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $activeSubscriptions; ?></h3>
                        <p>Active Subscriptions</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo formatPrice($totalSpent); ?></h3>
                        <p>Total Spent</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo count($tickets); ?></h3>
                        <p>Support Tickets</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo count($trials); ?></h3>
                        <p>Trials Used</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Active Subscriptions -->
                <div class="dashboard-card mb-4">
                    <div class="card-header">
                        <h4><i class="fas fa-tv"></i> Active Subscriptions</h4>
                        <a href="subscriptions.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> New Subscription
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($subscriptions)): ?>
                        <div class="empty-state">
                            <i class="fas fa-tv fa-3x text-muted mb-3"></i>
                            <h5>No Subscriptions Yet</h5>
                            <p>You don't have any active subscriptions. Start streaming today!</p>
                            <a href="subscriptions.php" class="btn btn-primary">
                                <i class="fas fa-shopping-cart"></i> Browse Plans
                            </a>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Plan</th>
                                        <th>Connections</th>
                                        <th>Status</th>
                                        <th>Expires</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($subscriptions as $subscription): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo ucfirst($subscription['plan_type']); ?> Plan</strong><br>
                                            <small class="text-muted"><?php echo formatPrice($subscription['price']); ?>/month</small>
                                        </td>
                                        <td><?php echo $subscription['connections']; ?> streams</td>
                                        <td>
                                            <span class="badge bg-<?php echo $subscription['status'] === 'active' ? 'success' : ($subscription['status'] === 'expired' ? 'danger' : 'warning'); ?>">
                                                <?php echo ucfirst($subscription['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($subscription['end_date'])); ?></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewSubscription(<?php echo $subscription['id']; ?>)">
                                                <i class="fas fa-eye"></i> View
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Support Tickets -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h4><i class="fas fa-headset"></i> Recent Support Tickets</h4>
                        <a href="contact.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus"></i> New Ticket
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($tickets)): ?>
                        <div class="empty-state">
                            <i class="fas fa-headset fa-3x text-muted mb-3"></i>
                            <h5>No Support Tickets</h5>
                            <p>You haven't created any support tickets yet.</p>
                        </div>
                        <?php else: ?>
                        <div class="tickets-list">
                            <?php foreach ($tickets as $ticket): ?>
                            <div class="ticket-item">
                                <div class="ticket-header">
                                    <h6>Ticket #<?php echo $ticket['id']; ?> - <?php echo htmlspecialchars($ticket['subject']); ?></h6>
                                    <span class="badge bg-<?php echo $ticket['status'] === 'open' ? 'primary' : ($ticket['status'] === 'resolved' ? 'success' : 'warning'); ?>">
                                        <?php echo ucfirst($ticket['status']); ?>
                                    </span>
                                </div>
                                <p class="ticket-message"><?php echo htmlspecialchars(substr($ticket['message'], 0, 100)) . '...'; ?></p>
                                <small class="text-muted">Created <?php echo timeAgo($ticket['created_at']); ?></small>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Account Info -->
                <div class="dashboard-card mb-4">
                    <div class="card-header">
                        <h4><i class="fas fa-user"></i> Account Information</h4>
                    </div>
                    <div class="card-body">
                        <div class="account-info">
                            <div class="info-item">
                                <label>Username:</label>
                                <span><?php echo htmlspecialchars($user['username']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Email:</label>
                                <span><?php echo htmlspecialchars($user['email']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Member Since:</label>
                                <span><?php echo date('M j, Y', strtotime($user['created_at'])); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Account Status:</label>
                                <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($user['status']); ?>
                                </span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm w-100" onclick="editProfile()">
                                <i class="fas fa-edit"></i> Edit Profile
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card mb-4">
                    <div class="card-header">
                        <h4><i class="fas fa-bolt"></i> Quick Actions</h4>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions">
                            <a href="subscriptions.php" class="quick-action-btn">
                                <i class="fas fa-shopping-cart"></i>
                                <span>New Subscription</span>
                            </a>
                            <a href="free-trial.php" class="quick-action-btn">
                                <i class="fas fa-play"></i>
                                <span>Free Trial</span>
                            </a>
                            <a href="tutorials.php" class="quick-action-btn">
                                <i class="fas fa-book"></i>
                                <span>Tutorials</span>
                            </a>
                            <a href="contact.php" class="quick-action-btn">
                                <i class="fas fa-headset"></i>
                                <span>Support</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h4><i class="fas fa-history"></i> Recent Activity</h4>
                    </div>
                    <div class="card-body">
                        <div class="activity-list">
                            <div class="activity-item">
                                <i class="fas fa-sign-in-alt text-success"></i>
                                <div>
                                    <p>Logged in</p>
                                    <small><?php echo $user['last_login'] ? timeAgo($user['last_login']) : 'Never'; ?></small>
                                </div>
                            </div>
                            <?php if (!empty($subscriptions)): ?>
                            <div class="activity-item">
                                <i class="fas fa-shopping-cart text-primary"></i>
                                <div>
                                    <p>Subscription created</p>
                                    <small><?php echo timeAgo($subscriptions[0]['created_at']); ?></small>
                                </div>
                            </div>
                            <?php endif; ?>
                            <div class="activity-item">
                                <i class="fas fa-user-plus text-info"></i>
                                <div>
                                    <p>Account created</p>
                                    <small><?php echo timeAgo($user['created_at']); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscription Details Modal -->
<div class="modal fade" id="subscriptionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Subscription Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="subscriptionDetails">
                <!-- Content loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

<style>
.client-area-section {
    background: #f8f9fa;
    min-height: 100vh;
}

.welcome-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    height: 100%;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 15px;
}

.stat-content h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.stat-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.dashboard-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.card-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: between;
    align-items: center;
}

.card-header h4 {
    margin: 0;
    color: #333;
}

.card-body {
    padding: 20px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.ticket-item {
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.ticket-item:last-child {
    border-bottom: none;
}

.ticket-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.ticket-header h6 {
    margin: 0;
    font-size: 0.9rem;
}

.ticket-message {
    margin: 5px 0;
    color: #666;
    font-size: 0.9rem;
}

.account-info .info-item {
    display: flex;
    justify-content: between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.account-info .info-item:last-child {
    border-bottom: none;
}

.account-info label {
    font-weight: 600;
    color: #333;
}

.quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 10px;
    background: #f8f9fa;
    border-radius: 10px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
}

.quick-action-btn i {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.activity-list .activity-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.activity-list .activity-item:last-child {
    border-bottom: none;
}

.activity-list .activity-item i {
    width: 30px;
    margin-right: 15px;
}

.activity-list .activity-item p {
    margin: 0;
    font-size: 0.9rem;
}

.activity-list .activity-item small {
    color: #666;
}

@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
function viewSubscription(subscriptionId) {
    // Load subscription details via AJAX
    makeAjaxRequest('api/get-subscription.php', {subscription_id: subscriptionId}, function(response) {
        if (response.success) {
            document.getElementById('subscriptionDetails').innerHTML = response.html;
            new bootstrap.Modal(document.getElementById('subscriptionModal')).show();
        } else {
            showNotification(response.message, 'error');
        }
    });
}

function editProfile() {
    // Redirect to profile edit page or show modal
    window.location.href = 'edit-profile.php';
}
</script>

<?php require_once 'includes/footer.php'; ?>
