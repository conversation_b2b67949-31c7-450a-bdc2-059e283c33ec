<?php
$pageTitle = "Free 24-Hour IPTV Trial";
$pageDescription = "Get your free 24-hour IPTV trial. Test our premium service with 16000+ channels and 40000+ VOD content before you buy.";

// Handle trial request form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showAlert('Invalid security token. Please try again.', 'danger');
    } else {
        $name = sanitizeInput($_POST['name'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $country = sanitizeInput($_POST['country'] ?? '');
        $device = sanitizeInput($_POST['device'] ?? '');
        $experience = sanitizeInput($_POST['experience'] ?? '');
        
        // Validation
        $errors = [];
        
        if (empty($name)) $errors[] = 'Name is required';
        if (empty($email)) $errors[] = 'Email is required';
        if (empty($country)) $errors[] = 'Country is required';
        if (empty($device)) $errors[] = 'Device information is required';
        
        if (!validateEmail($email)) {
            $errors[] = 'Please enter a valid email address';
        }
        
        if (empty($errors)) {
            $deviceInfo = "Device: {$device}, Experience: {$experience}, Country: {$country}";
            $result = createTrial($email, $deviceInfo);
            
            if ($result['success']) {
                showAlert('Trial created successfully! Check your email for login credentials.', 'success');
                
                // Redirect to prevent form resubmission
                redirect('free-trial.php?success=1');
            } else {
                showAlert($result['message'], 'danger');
            }
        } else {
            foreach ($errors as $error) {
                showAlert($error, 'danger');
                break; // Show only first error
            }
        }
    }
}

require_once 'includes/header.php';

// Check if trial was successful
$showSuccess = isset($_GET['success']) && $_GET['success'] == '1';
?>

    <!-- Hero Section -->
    <section class="trial-hero py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">Free 24-Hour IPTV Trial</h1>
                        <p class="hero-subtitle">Test our premium IPTV service with full access to 16,000+ channels and 40,000+ VOD content. No payment required!</p>
                        
                        <div class="trial-features">
                            <div class="feature-item">
                                <i class="fas fa-tv text-primary"></i>
                                <span>16,000+ Live Channels</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-film text-primary"></i>
                                <span>40,000+ Movies & Series</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-hd-video text-primary"></i>
                                <span>4K/HD Quality</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-clock text-primary"></i>
                                <span>24 Hours Full Access</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="trial-image text-center">
                        <img src="<?php echo APP_URL; ?>/assets/images/placeholder.svg" alt="Free IPTV Trial" class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <?php if ($showSuccess): ?>
    <!-- Success Message -->
    <section class="success-section py-5 bg-success text-white">
        <div class="container text-center">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <i class="fas fa-check-circle fa-5x mb-4"></i>
                    <h2>Trial Created Successfully!</h2>
                    <p class="lead">Your 24-hour free trial has been activated. Check your email for login credentials and setup instructions.</p>
                    <div class="mt-4">
                        <a href="tutorials.php" class="btn btn-light btn-lg me-3">
                            <i class="fas fa-play"></i> Setup Tutorial
                        </a>
                        <a href="subscriptions.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-shopping-cart"></i> Subscribe Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php else: ?>
    <!-- Trial Request Form -->
    <section class="trial-form-section py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="trial-form-card">
                        <div class="form-header text-center mb-4">
                            <h2>Request Your Free Trial</h2>
                            <p>Fill out the form below to get instant access to our IPTV service</p>
                        </div>
                        
                        <form method="POST" class="trial-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required
                                           value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email" required
                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="country" class="form-label">Country *</label>
                                    <select class="form-control" id="country" name="country" required>
                                        <option value="">Select Your Country</option>
                                        <option value="US" <?php echo (isset($_POST['country']) && $_POST['country'] == 'US') ? 'selected' : ''; ?>>United States</option>
                                        <option value="CA" <?php echo (isset($_POST['country']) && $_POST['country'] == 'CA') ? 'selected' : ''; ?>>Canada</option>
                                        <option value="UK" <?php echo (isset($_POST['country']) && $_POST['country'] == 'UK') ? 'selected' : ''; ?>>United Kingdom</option>
                                        <option value="DE" <?php echo (isset($_POST['country']) && $_POST['country'] == 'DE') ? 'selected' : ''; ?>>Germany</option>
                                        <option value="FR" <?php echo (isset($_POST['country']) && $_POST['country'] == 'FR') ? 'selected' : ''; ?>>France</option>
                                        <option value="IT" <?php echo (isset($_POST['country']) && $_POST['country'] == 'IT') ? 'selected' : ''; ?>>Italy</option>
                                        <option value="ES" <?php echo (isset($_POST['country']) && $_POST['country'] == 'ES') ? 'selected' : ''; ?>>Spain</option>
                                        <option value="AU" <?php echo (isset($_POST['country']) && $_POST['country'] == 'AU') ? 'selected' : ''; ?>>Australia</option>
                                        <option value="OTHER">Other</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="device" class="form-label">Primary Device *</label>
                                    <select class="form-control" id="device" name="device" required>
                                        <option value="">Select Your Device</option>
                                        <option value="Android TV" <?php echo (isset($_POST['device']) && $_POST['device'] == 'Android TV') ? 'selected' : ''; ?>>Android TV</option>
                                        <option value="Smart TV" <?php echo (isset($_POST['device']) && $_POST['device'] == 'Smart TV') ? 'selected' : ''; ?>>Smart TV</option>
                                        <option value="Fire Stick" <?php echo (isset($_POST['device']) && $_POST['device'] == 'Fire Stick') ? 'selected' : ''; ?>>Amazon Fire Stick</option>
                                        <option value="MAG Box" <?php echo (isset($_POST['device']) && $_POST['device'] == 'MAG Box') ? 'selected' : ''; ?>>MAG Box</option>
                                        <option value="iPhone/iPad" <?php echo (isset($_POST['device']) && $_POST['device'] == 'iPhone/iPad') ? 'selected' : ''; ?>>iPhone/iPad</option>
                                        <option value="Android Phone" <?php echo (isset($_POST['device']) && $_POST['device'] == 'Android Phone') ? 'selected' : ''; ?>>Android Phone/Tablet</option>
                                        <option value="PC/Mac" <?php echo (isset($_POST['device']) && $_POST['device'] == 'PC/Mac') ? 'selected' : ''; ?>>PC/Mac</option>
                                        <option value="Other" <?php echo (isset($_POST['device']) && $_POST['device'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="experience" class="form-label">IPTV Experience</label>
                                <select class="form-control" id="experience" name="experience">
                                    <option value="">Select Your Experience Level</option>
                                    <option value="Beginner" <?php echo (isset($_POST['experience']) && $_POST['experience'] == 'Beginner') ? 'selected' : ''; ?>>Beginner - New to IPTV</option>
                                    <option value="Intermediate" <?php echo (isset($_POST['experience']) && $_POST['experience'] == 'Intermediate') ? 'selected' : ''; ?>>Intermediate - Some experience</option>
                                    <option value="Advanced" <?php echo (isset($_POST['experience']) && $_POST['experience'] == 'Advanced') ? 'selected' : ''; ?>>Advanced - Very experienced</option>
                                </select>
                            </div>
                            
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="agree_trial" name="agree_trial" required>
                                    <label class="form-check-label" for="agree_trial">
                                        I agree that this is a one-time trial and I understand the terms of service
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-play"></i> Get My Free Trial
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- How It Works -->
    <section class="how-it-works py-5 bg-light">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">How It Works</h2>
                    <p class="section-subtitle">Get started with your free trial in 3 simple steps</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <h4>Fill the Form</h4>
                        <p>Complete the trial request form with your details</p>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <h4>Receive Credentials</h4>
                        <p>Get your login details via email within minutes</p>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <h4>Start Streaming</h4>
                        <p>Install our app and enjoy 24 hours of premium content</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
.trial-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.trial-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.trial-form-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.step-card {
    text-align: center;
    padding: 30px 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    height: 100%;
}

.step-number {
    width: 60px;
    height: 60px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 20px;
}

.success-section {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .trial-features {
        grid-template-columns: 1fr;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
