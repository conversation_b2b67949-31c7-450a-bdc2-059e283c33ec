<?php
$pageTitle = "IPTV Setup Tutorials";
$pageDescription = "Step-by-step tutorials for setting up IPTV on all devices. Learn how to install and configure IPTV players on Smart TV, Android, iOS, MAG Box, and more.";

require_once 'includes/header.php';
?>

    <!-- Hero Section -->
    <section class="tutorials-hero py-5">
        <div class="container">
            <div class="row text-center">
                <div class="col-12">
                    <h1 class="hero-title">IPTV Setup Tutorials</h1>
                    <p class="hero-subtitle">Step-by-step guides to get you streaming in minutes</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Device Categories -->
    <section class="device-categories py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">Choose Your Device</h2>
                    <p class="section-subtitle">Select your device below for detailed setup instructions</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="device-card" data-device="android-tv">
                        <div class="device-icon">
                            <i class="fab fa-android"></i>
                        </div>
                        <h4>Android TV</h4>
                        <p>Smart TVs, Android TV Boxes, NVIDIA Shield</p>
                        <button class="btn btn-primary">View Tutorial</button>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="device-card" data-device="firestick">
                        <div class="device-icon">
                            <i class="fab fa-amazon"></i>
                        </div>
                        <h4>Fire Stick/TV</h4>
                        <p>Amazon Fire Stick, Fire TV, Fire TV Cube</p>
                        <button class="btn btn-primary">View Tutorial</button>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="device-card" data-device="ios">
                        <div class="device-icon">
                            <i class="fab fa-apple"></i>
                        </div>
                        <h4>iOS Devices</h4>
                        <p>iPhone, iPad, Apple TV</p>
                        <button class="btn btn-primary">View Tutorial</button>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="device-card" data-device="mag-box">
                        <div class="device-icon">
                            <i class="fas fa-cube"></i>
                        </div>
                        <h4>MAG Box</h4>
                        <p>MAG 254, MAG 256, MAG 322, MAG 424</p>
                        <button class="btn btn-primary">View Tutorial</button>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="device-card" data-device="smart-tv">
                        <div class="device-icon">
                            <i class="fas fa-tv"></i>
                        </div>
                        <h4>Smart TV</h4>
                        <p>Samsung, LG, Sony Smart TVs</p>
                        <button class="btn btn-primary">View Tutorial</button>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="device-card" data-device="pc-mac">
                        <div class="device-icon">
                            <i class="fas fa-desktop"></i>
                        </div>
                        <h4>PC/Mac</h4>
                        <p>Windows, macOS, Linux</p>
                        <button class="btn btn-primary">View Tutorial</button>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="device-card" data-device="android-mobile">
                        <div class="device-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4>Android Mobile</h4>
                        <p>Android phones and tablets</p>
                        <button class="btn btn-primary">View Tutorial</button>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="device-card" data-device="enigma2">
                        <div class="device-icon">
                            <i class="fas fa-satellite-dish"></i>
                        </div>
                        <h4>Enigma2</h4>
                        <p>Dreambox, VU+, OpenATV</p>
                        <button class="btn btn-primary">View Tutorial</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tutorial Content -->
    <section class="tutorial-content py-5 bg-light">
        <div class="container">
            <!-- Android TV Tutorial -->
            <div class="tutorial-section" id="android-tv" style="display: none;">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="tutorial-card">
                            <div class="tutorial-header">
                                <h3><i class="fab fa-android"></i> Android TV Setup</h3>
                                <p>Follow these steps to set up IPTV on your Android TV device</p>
                            </div>
                            
                            <div class="tutorial-steps">
                                <div class="step">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <h5>Download IPTV Smarters Pro</h5>
                                        <p>Go to Google Play Store on your Android TV and search for "IPTV Smarters Pro". Download and install the app.</p>
                                    </div>
                                </div>
                                
                                <div class="step">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <h5>Open the App</h5>
                                        <p>Launch IPTV Smarters Pro and select "Login with Xtream Codes API".</p>
                                    </div>
                                </div>
                                
                                <div class="step">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <h5>Enter Your Credentials</h5>
                                        <p>Enter the following information from your subscription email:</p>
                                        <ul>
                                            <li><strong>Server URL:</strong> Your server address</li>
                                            <li><strong>Username:</strong> Your IPTV username</li>
                                            <li><strong>Password:</strong> Your IPTV password</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="step">
                                    <div class="step-number">4</div>
                                    <div class="step-content">
                                        <h5>Start Streaming</h5>
                                        <p>Click "Add User" and wait for the app to load your channels. You can now browse and watch your favorite content!</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="tutorial-footer">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Alternative Apps:</h6>
                                        <ul>
                                            <li>Perfect Player IPTV</li>
                                            <li>TiviMate IPTV Player</li>
                                            <li>IPTV Extreme</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Need Help?</h6>
                                        <p>If you encounter any issues, please contact our support team.</p>
                                        <a href="contact.php" class="btn btn-outline-primary btn-sm">Get Support</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fire Stick Tutorial -->
            <div class="tutorial-section" id="firestick" style="display: none;">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="tutorial-card">
                            <div class="tutorial-header">
                                <h3><i class="fab fa-amazon"></i> Amazon Fire Stick Setup</h3>
                                <p>Complete guide to install IPTV on your Fire Stick device</p>
                            </div>
                            
                            <div class="tutorial-steps">
                                <div class="step">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <h5>Enable Apps from Unknown Sources</h5>
                                        <p>Go to Settings > My Fire TV > Developer Options > Apps from Unknown Sources and turn it ON.</p>
                                    </div>
                                </div>
                                
                                <div class="step">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <h5>Install Downloader App</h5>
                                        <p>Search for "Downloader" in the Amazon App Store and install it.</p>
                                    </div>
                                </div>
                                
                                <div class="step">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <h5>Download IPTV Smarters Pro</h5>
                                        <p>Open Downloader and enter this URL: <code>https://iptvsmarters.com/download</code></p>
                                        <p>Download and install the APK file.</p>
                                    </div>
                                </div>
                                
                                <div class="step">
                                    <div class="step-number">4</div>
                                    <div class="step-content">
                                        <h5>Configure the App</h5>
                                        <p>Open IPTV Smarters Pro, select "Login with Xtream Codes API" and enter your credentials.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Default Message -->
            <div class="tutorial-section" id="default-message">
                <div class="row">
                    <div class="col-lg-8 mx-auto text-center">
                        <div class="default-tutorial">
                            <i class="fas fa-play-circle fa-5x text-primary mb-4"></i>
                            <h3>Select Your Device</h3>
                            <p>Choose your device from the options above to view detailed setup instructions.</p>
                            <p>Can't find your device? <a href="contact.php">Contact our support team</a> for personalized assistance.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Popular Apps -->
    <section class="popular-apps py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">Recommended IPTV Apps</h2>
                    <p class="section-subtitle">Best apps for streaming your IPTV content</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="app-card">
                        <div class="app-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h5>IPTV Smarters Pro</h5>
                        <p>Most popular and user-friendly IPTV player with EPG support and catch-up TV.</p>
                        <div class="app-platforms">
                            <span class="badge bg-success">Android</span>
                            <span class="badge bg-primary">iOS</span>
                            <span class="badge bg-info">Fire TV</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="app-card">
                        <div class="app-icon">
                            <i class="fas fa-tv"></i>
                        </div>
                        <h5>TiviMate</h5>
                        <p>Premium IPTV player with advanced features, recording capabilities, and multiple playlist support.</p>
                        <div class="app-platforms">
                            <span class="badge bg-success">Android TV</span>
                            <span class="badge bg-info">Fire TV</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="app-card">
                        <div class="app-icon">
                            <i class="fas fa-play"></i>
                        </div>
                        <h5>Perfect Player</h5>
                        <p>Lightweight and fast IPTV player with customizable interface and EPG support.</p>
                        <div class="app-platforms">
                            <span class="badge bg-success">Android</span>
                            <span class="badge bg-info">Fire TV</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
.tutorials-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.device-card {
    background: white;
    border-radius: 15px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    height: 100%;
    transition: all 0.3s ease;
    cursor: pointer;
}

.device-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.device-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
}

.device-card h4 {
    margin-bottom: 10px;
    color: #333;
}

.device-card p {
    color: #666;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.tutorial-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.tutorial-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f8f9fa;
}

.tutorial-header h3 {
    color: #333;
    margin-bottom: 10px;
}

.tutorial-header p {
    color: #666;
    margin: 0;
}

.step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid #eee;
}

.step:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.step-number {
    width: 50px;
    height: 50px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-content h5 {
    margin-bottom: 10px;
    color: #333;
}

.step-content p {
    color: #666;
    margin-bottom: 10px;
}

.step-content ul {
    color: #666;
    margin-left: 20px;
}

.step-content code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.9rem;
}

.tutorial-footer {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #f8f9fa;
}

.tutorial-footer h6 {
    color: #333;
    margin-bottom: 15px;
}

.tutorial-footer ul {
    list-style: none;
    padding: 0;
}

.tutorial-footer li {
    padding: 3px 0;
    color: #666;
}

.default-tutorial {
    padding: 60px 20px;
    color: #666;
}

.app-card {
    background: white;
    border-radius: 15px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    height: 100%;
}

.app-icon {
    width: 60px;
    height: 60px;
    background: #f8f9fa;
    color: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 1.5rem;
}

.app-card h5 {
    margin-bottom: 15px;
    color: #333;
}

.app-card p {
    color: #666;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.app-platforms {
    display: flex;
    justify-content: center;
    gap: 5px;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .step {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        margin: 0 auto 15px;
    }
    
    .tutorial-footer .row {
        text-align: center;
    }
}
</style>

<script>
// Tutorial navigation
document.querySelectorAll('.device-card').forEach(card => {
    card.addEventListener('click', function() {
        const device = this.dataset.device;
        showTutorial(device);
        
        // Scroll to tutorial section
        document.querySelector('.tutorial-content').scrollIntoView({
            behavior: 'smooth'
        });
    });
});

function showTutorial(device) {
    // Hide all tutorial sections
    document.querySelectorAll('.tutorial-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // Show selected tutorial or default message
    const targetSection = document.getElementById(device) || document.getElementById('default-message');
    targetSection.style.display = 'block';
    
    // Update active device card
    document.querySelectorAll('.device-card').forEach(card => {
        card.classList.remove('active');
    });
    
    const activeCard = document.querySelector(`[data-device="${device}"]`);
    if (activeCard) {
        activeCard.classList.add('active');
    }
}

// Show default message initially
showTutorial('default');
</script>

<?php require_once 'includes/footer.php'; ?>
