    <!-- Live Chat Widget -->
    <?php if ($site_settings['live_chat_enabled']): ?>
    <div id="liveChatWidget" class="live-chat-widget">
        <button class="chat-button" onclick="toggleChat()" id="chatButton">
            <i class="fas fa-comments"></i>
            <span class="notification-badge" id="notificationBadge" style="display: none;">1</span>
        </button>

        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <div class="chat-header-content">
                    <div class="chat-avatar">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="chat-header-info">
                        <h5>Live Support</h5>
                        <p>We're online now</p>
                    </div>
                </div>
                <button class="close-chat" onclick="toggleChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="chat-body" id="chatBody">
                <div class="chat-message bot">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div>
                        <p>👋 Hello! Welcome to <?php echo APP_NAME; ?>! How can I help you today?</p>
                        <div class="quick-replies">
                            <span class="quick-reply" onclick="sendQuickReply('I need help with setup')">Setup Help</span>
                            <span class="quick-reply" onclick="sendQuickReply('I want to subscribe')">Subscribe</span>
                            <span class="quick-reply" onclick="sendQuickReply('I need technical support')">Tech Support</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chat-input">
                <input type="text" placeholder="Type your message..." id="chatInput" onkeypress="handleChatKeyPress(event)">
                <button class="chat-send-btn" onclick="sendMessage()" id="sendButton">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="footer py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-section">
                        <h5><?php echo APP_NAME; ?></h5>
                        <p>Your trusted premium IPTV service provider with years of experience and thousands of satisfied customers worldwide.</p>
                        <div class="social-links">
                            <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-telegram"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-section">
                        <h6>Quick Links</h6>
                        <ul class="footer-links">
                            <li><a href="index.php">Home</a></li>
                            <li><a href="subscriptions.php">Subscriptions</a></li>
                            <li><a href="free-trial.php">Free Trial</a></li>
                            <li><a href="reseller.php">Reseller</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-section">
                        <h6>Support</h6>
                        <ul class="footer-links">
                            <li><a href="tutorials.php">Tutorials</a></li>
                            <li><a href="contact.php">Contact Us</a></li>
                            <li><a href="faq.php">FAQ</a></li>
                            <?php if (isLoggedIn()): ?>
                            <li><a href="client-area.php">Client Area</a></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-section">
                        <h6>Contact Info</h6>
                        <div class="contact-info">
                            <p><i class="fas fa-envelope"></i> <?php echo SUPPORT_EMAIL; ?></p>
                            <p><i class="fas fa-clock"></i> 24/7 Support Available</p>
                            <p><i class="fas fa-shield-alt"></i> Secure & Reliable Service</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <hr class="footer-divider">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="copyright">&copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-links-inline">
                        <a href="privacy-policy.php">Privacy Policy</a>
                        <a href="terms-of-service.php">Terms of Service</a>
                        <a href="refund-policy.php">Refund Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global JavaScript variables
        window.APP_URL = '<?php echo APP_URL; ?>';
        window.CSRF_TOKEN = '<?php echo generateCSRFToken(); ?>';
        
        // Fallback navigation toggle for mobile menu
        document.addEventListener('DOMContentLoaded', function() {
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');
            
            if (navbarToggler && navbarCollapse) {
                navbarToggler.addEventListener('click', function() {
                    navbarCollapse.classList.toggle('show');
                });
                
                // Close menu when clicking outside
                document.addEventListener('click', function(e) {
                    if (!navbarToggler.contains(e.target) && !navbarCollapse.contains(e.target)) {
                        navbarCollapse.classList.remove('show');
                    }
                });
                
                // Close menu when clicking on nav links
                const navLinks = navbarCollapse.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        if (window.innerWidth < 992) {
                            navbarCollapse.classList.remove('show');
                        }
                    });
                });
            }
            
            // Auto-dismiss alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.classList.remove('show');
                        setTimeout(() => alert.remove(), 150);
                    }
                }, 5000);
            });
        });
        
        // AJAX helper function
        function makeAjaxRequest(url, data, callback, method = 'POST') {
            const xhr = new XMLHttpRequest();
            xhr.open(method, url, true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            callback(response);
                        } catch (e) {
                            callback({success: false, message: 'Invalid response from server'});
                        }
                    } else {
                        callback({success: false, message: 'Server error occurred'});
                    }
                }
            };
            
            // Add CSRF token to data
            if (typeof data === 'string') {
                data += '&csrf_token=' + encodeURIComponent(window.CSRF_TOKEN);
            } else if (typeof data === 'object') {
                data.csrf_token = window.CSRF_TOKEN;
                data = Object.keys(data).map(key => key + '=' + encodeURIComponent(data[key])).join('&');
            }
            
            xhr.send(data);
        }
        
        // Show notification function
        function showNotification(message, type = 'info', duration = 5000) {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show notification-popup`;
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                max-width: 500px;
            `;
            
            document.body.appendChild(notification);
            
            // Auto remove
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 150);
                }
            }, duration);
        }
    </script>
    <script src="<?php echo APP_URL; ?>/assets/js/main.js"></script>
    <?php if ($site_settings['live_chat_enabled']): ?>
    <script src="<?php echo APP_URL; ?>/assets/js/livechat.js"></script>
    <?php endif; ?>
</body>
</html>
