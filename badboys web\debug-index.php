<?php
// Debug version of index page to test CSS loading
require_once 'config/config.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - BadBoyz IPTV</title>
    
    <!-- Direct CSS Links for Testing -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://badboyzmedia.org/assets/css/style.css">
    
    <style>
        .debug-info {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 2px solid #dee2e6;
        }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <!-- Debug Information -->
    <div class="container mt-4">
        <div class="debug-info">
            <h3><i class="fas fa-bug"></i> Debug Information</h3>
            <p><strong>App URL:</strong> <?php echo APP_URL; ?></p>
            <p><strong>CSS Path:</strong> <?php echo APP_URL; ?>/assets/css/style.css</p>
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>Bootstrap Status:</strong> <span id="bootstrap-status" class="status-error">❌ Not Loaded</span></p>
            <p><strong>Font Awesome Status:</strong> <i class="fas fa-check status-ok"></i> <span class="status-ok">✅ Loaded</span></p>
        </div>
    </div>

    <!-- Test Hero Section -->
    <section class="hero-section">
        <div class="hero-bg">
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-6">
                        <div class="hero-content">
                            <h1 class="hero-title">BadBoyz IPTV - Debug Test</h1>
                            <p class="hero-subtitle">Testing if CSS is loading properly</p>
                            
                            <div class="hero-buttons">
                                <a href="subscriptions.php" class="btn btn-primary btn-lg me-3">Subscribe Now</a>
                                <a href="free-trial.php" class="btn btn-outline-light btn-lg">Free Trial</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="hero-image">
                            <img src="<?php echo APP_URL; ?>/assets/images/placeholder.svg" alt="IPTV Streaming" class="img-fluid">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Test Bootstrap Components -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-4">Bootstrap Component Test</h2>
            
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-tv fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">16,000+ Channels</h5>
                            <p class="card-text">Live TV channels from around the world</p>
                            <a href="#" class="btn btn-primary">Learn More</a>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-film fa-3x text-success mb-3"></i>
                            <h5 class="card-title">40,000+ VOD</h5>
                            <p class="card-text">Movies and TV shows on demand</p>
                            <a href="#" class="btn btn-success">Watch Now</a>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-hd-video fa-3x text-info mb-3"></i>
                            <h5 class="card-title">4K Quality</h5>
                            <p class="card-text">Ultra HD streaming quality</p>
                            <a href="#" class="btn btn-info">Get Started</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Alert Test -->
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle"></i>
                <strong>Info:</strong> This is a test alert to check Bootstrap styling.
            </div>
            
            <!-- Button Test -->
            <div class="text-center">
                <button class="btn btn-primary btn-lg me-2">Primary Button</button>
                <button class="btn btn-success btn-lg me-2">Success Button</button>
                <button class="btn btn-warning btn-lg me-2">Warning Button</button>
                <button class="btn btn-danger btn-lg">Danger Button</button>
            </div>
        </div>
    </section>

    <!-- Navigation Test -->
    <div class="container">
        <div class="debug-info">
            <h4>Navigation Links</h4>
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li><a href="index.php">🏠 Homepage</a></li>
                        <li><a href="subscriptions.php">💳 Subscriptions</a></li>
                        <li><a href="free-trial.php">🆓 Free Trial</a></li>
                        <li><a href="about.php">ℹ️ About Us</a></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li><a href="contact.php">📞 Contact</a></li>
                        <li><a href="tutorials.php">📚 Tutorials</a></li>
                        <li><a href="login.php">🔐 Login</a></li>
                        <li><a href="register.php">👤 Register</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Show current URL
            document.getElementById('current-url').textContent = window.location.href;
            
            // Test Bootstrap
            const testDiv = document.createElement('div');
            testDiv.className = 'container';
            document.body.appendChild(testDiv);
            
            const styles = window.getComputedStyle(testDiv);
            const hasBootstrap = styles.paddingLeft !== '0px' || styles.paddingRight !== '0px';
            
            if (hasBootstrap || typeof bootstrap !== 'undefined') {
                document.getElementById('bootstrap-status').innerHTML = '✅ Loaded';
                document.getElementById('bootstrap-status').className = 'status-ok';
            }
            
            document.body.removeChild(testDiv);
            
            // Log CSS loading info
            console.log('CSS Debug Info:');
            console.log('App URL:', '<?php echo APP_URL; ?>');
            console.log('CSS Path:', '<?php echo APP_URL; ?>/assets/css/style.css');
            console.log('Bootstrap loaded:', typeof bootstrap !== 'undefined');
        });
    </script>
</body>
</html>
