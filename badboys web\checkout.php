<?php
$pageTitle = "Checkout - Complete Your Order";
$pageDescription = "Complete your IPTV subscription purchase securely. Choose your payment method and start streaming immediately.";

require_once 'includes/header.php';

// Get plan details from URL parameters
$planType = sanitizeInput($_GET['plan'] ?? '');
$connections = intval($_GET['connections'] ?? 0);
$price = floatval($_GET['price'] ?? 0);

// Validate plan
if (!$planType || !isset($subscription_plans[$planType])) {
    showAlert('Invalid subscription plan selected.', 'danger');
    redirect('subscriptions.php');
}

$selectedPlan = $subscription_plans[$planType];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showAlert('Invalid security token. Please try again.', 'danger');
    } else {
        // For demo purposes, we'll simulate a successful payment
        // In production, integrate with PayPal, Stripe, or other payment processors
        
        if (isLoggedIn()) {
            // Create subscription for logged-in user
            $subscriptionId = createSubscription($_SESSION['user_id'], $planType, 'demo_payment', 'DEMO_' . time());
            
            if ($subscriptionId) {
                showAlert('Subscription created successfully! Check your email for IPTV credentials.', 'success');
                redirect('client-area.php');
            } else {
                showAlert('Failed to create subscription. Please try again.', 'danger');
            }
        } else {
            // Redirect to registration with plan info
            $_SESSION['checkout_plan'] = $planType;
            showAlert('Please create an account to complete your purchase.', 'info');
            redirect('register.php');
        }
    }
}
?>

    <!-- Checkout Section -->
    <section class="checkout-section py-5">
        <div class="container">
            <div class="row">
                <!-- Order Summary -->
                <div class="col-lg-4 order-lg-2 mb-4">
                    <div class="order-summary">
                        <h4><i class="fas fa-shopping-cart"></i> Order Summary</h4>
                        
                        <div class="plan-details">
                            <div class="plan-header">
                                <h5><?php echo htmlspecialchars($selectedPlan['name']); ?></h5>
                                <?php if (isset($selectedPlan['popular']) && $selectedPlan['popular']): ?>
                                <span class="badge bg-primary">Most Popular</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="plan-features">
                                <ul>
                                    <?php foreach ($selectedPlan['features'] as $feature): ?>
                                    <li><i class="fas fa-check text-success"></i> <?php echo htmlspecialchars($feature); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            
                            <div class="plan-pricing">
                                <div class="price-row">
                                    <span>Monthly Subscription</span>
                                    <span><?php echo formatPrice($selectedPlan['price']); ?></span>
                                </div>
                                <div class="price-row">
                                    <span>Setup Fee</span>
                                    <span class="text-success">FREE</span>
                                </div>
                                <hr>
                                <div class="price-row total">
                                    <span><strong>Total</strong></span>
                                    <span><strong><?php echo formatPrice($selectedPlan['price']); ?></strong></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="guarantee-badge">
                            <i class="fas fa-shield-alt"></i>
                            <div>
                                <strong>7-Day Money Back Guarantee</strong>
                                <small>Not satisfied? Get a full refund within 7 days</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Checkout Form -->
                <div class="col-lg-8 order-lg-1">
                    <div class="checkout-form">
                        <h2>Complete Your Order</h2>
                        <p class="text-muted">Choose your payment method and complete your IPTV subscription</p>
                        
                        <?php if (!isLoggedIn()): ?>
                        <!-- Account Creation Notice -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Account Required:</strong> You need to create an account to complete your purchase. 
                            <a href="register.php" class="alert-link">Create account now</a> or 
                            <a href="login.php" class="alert-link">login</a> if you already have one.
                        </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="payment-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="plan_type" value="<?php echo htmlspecialchars($planType); ?>">
                            <input type="hidden" name="price" value="<?php echo $selectedPlan['price']; ?>">
                            
                            <!-- Payment Methods -->
                            <div class="payment-methods mb-4">
                                <h5>Select Payment Method</h5>
                                
                                <div class="payment-option">
                                    <input type="radio" id="paypal" name="payment_method" value="paypal" checked>
                                    <label for="paypal" class="payment-label">
                                        <div class="payment-icon">
                                            <i class="fab fa-paypal"></i>
                                        </div>
                                        <div class="payment-info">
                                            <strong>PayPal</strong>
                                            <small>Pay securely with your PayPal account</small>
                                        </div>
                                    </label>
                                </div>
                                
                                <div class="payment-option">
                                    <input type="radio" id="card" name="payment_method" value="card">
                                    <label for="card" class="payment-label">
                                        <div class="payment-icon">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                        <div class="payment-info">
                                            <strong>Credit/Debit Card</strong>
                                            <small>Visa, MasterCard, American Express</small>
                                        </div>
                                    </label>
                                </div>
                                
                                <div class="payment-option">
                                    <input type="radio" id="crypto" name="payment_method" value="crypto">
                                    <label for="crypto" class="payment-label">
                                        <div class="payment-icon">
                                            <i class="fab fa-bitcoin"></i>
                                        </div>
                                        <div class="payment-info">
                                            <strong>Cryptocurrency</strong>
                                            <small>Bitcoin, Ethereum, USDT</small>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Card Details (shown when card is selected) -->
                            <div class="card-details" id="cardDetails" style="display: none;">
                                <h5>Card Information</h5>
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <label for="card_number" class="form-label">Card Number</label>
                                        <input type="text" class="form-control" id="card_number" placeholder="1234 5678 9012 3456">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="expiry" class="form-label">Expiry Date</label>
                                        <input type="text" class="form-control" id="expiry" placeholder="MM/YY">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="cvv" class="form-label">CVV</label>
                                        <input type="text" class="form-control" id="cvv" placeholder="123">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Billing Information -->
                            <?php if (isLoggedIn()): ?>
                            <div class="billing-info mb-4">
                                <h5>Billing Information</h5>
                                <div class="current-user-info">
                                    <p><strong>Account:</strong> <?php echo htmlspecialchars($_SESSION['username']); ?></p>
                                    <p><strong>Email:</strong> <?php echo htmlspecialchars($_SESSION['email']); ?></p>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <!-- Terms and Conditions -->
                            <div class="terms-section mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="agree_terms" name="agree_terms" required>
                                    <label class="form-check-label" for="agree_terms">
                                        I agree to the <a href="terms-of-service.php" target="_blank">Terms of Service</a> and <a href="privacy-policy.php" target="_blank">Privacy Policy</a>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="agree_refund" name="agree_refund" required>
                                    <label class="form-check-label" for="agree_refund">
                                        I understand the <a href="refund-policy.php" target="_blank">7-day refund policy</a>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <?php if (isLoggedIn()): ?>
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-lock"></i> Complete Order - <?php echo formatPrice($selectedPlan['price']); ?>
                            </button>
                            <?php else: ?>
                            <a href="register.php" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-user-plus"></i> Create Account to Continue
                            </a>
                            <?php endif; ?>
                        </form>
                        
                        <!-- Security Features -->
                        <div class="security-features mt-4">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                    <small>SSL Encrypted</small>
                                </div>
                                <div class="col-md-4">
                                    <i class="fas fa-lock fa-2x text-success mb-2"></i>
                                    <small>Secure Payment</small>
                                </div>
                                <div class="col-md-4">
                                    <i class="fas fa-undo fa-2x text-success mb-2"></i>
                                    <small>Money Back Guarantee</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
.checkout-section {
    background: #f8f9fa;
    min-height: 100vh;
}

.order-summary {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    position: sticky;
    top: 20px;
}

.order-summary h4 {
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.plan-header h5 {
    margin: 0;
    color: #333;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin-bottom: 20px;
}

.plan-features li {
    padding: 5px 0;
    font-size: 0.9rem;
}

.plan-features i {
    margin-right: 8px;
}

.price-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
}

.price-row.total {
    font-size: 1.1rem;
    border-top: 2px solid #f8f9fa;
    padding-top: 15px;
    margin-top: 10px;
}

.guarantee-badge {
    background: #e8f5e8;
    border: 1px solid #28a745;
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.guarantee-badge i {
    color: #28a745;
    font-size: 1.5rem;
}

.guarantee-badge strong {
    color: #28a745;
    display: block;
}

.guarantee-badge small {
    color: #666;
    display: block;
}

.checkout-form {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.checkout-form h2 {
    color: #333;
    margin-bottom: 10px;
}

.payment-option {
    margin-bottom: 15px;
}

.payment-option input[type="radio"] {
    display: none;
}

.payment-label {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-label:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.payment-option input[type="radio"]:checked + .payment-label {
    border-color: #007bff;
    background: #e3f2fd;
}

.payment-icon {
    width: 50px;
    height: 50px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.5rem;
    color: #007bff;
}

.payment-info strong {
    display: block;
    color: #333;
}

.payment-info small {
    color: #666;
}

.card-details {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.card-details h5 {
    margin-bottom: 15px;
    color: #333;
}

.billing-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
}

.current-user-info p {
    margin: 5px 0;
    color: #666;
}

.terms-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
}

.security-features {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    color: #666;
}

@media (max-width: 768px) {
    .checkout-form {
        padding: 20px;
    }
    
    .order-summary {
        position: relative;
        margin-bottom: 30px;
    }
    
    .payment-label {
        flex-direction: column;
        text-align: center;
    }
    
    .payment-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
}
</style>

<script>
// Payment method selection
document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const cardDetails = document.getElementById('cardDetails');
        if (this.value === 'card') {
            cardDetails.style.display = 'block';
        } else {
            cardDetails.style.display = 'none';
        }
    });
});

// Card number formatting
document.getElementById('card_number')?.addEventListener('input', function(e) {
    let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
    let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
    e.target.value = formattedValue;
});

// Expiry date formatting
document.getElementById('expiry')?.addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length >= 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
    }
    e.target.value = value;
});

// CVV validation
document.getElementById('cvv')?.addEventListener('input', function(e) {
    e.target.value = e.target.value.replace(/[^0-9]/g, '').substring(0, 4);
});
</script>

<?php require_once 'includes/footer.php'; ?>
