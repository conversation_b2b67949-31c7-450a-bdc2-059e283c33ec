// Checkout functionality for StreamPro IPTV

// Plan configurations with connection options
const plans = {
    // 2 Connections Plans
    '1month_2': { name: '1 Month - 2 Connections', price: 15, duration: '1 month', connections: 2 },
    '3month_2': { name: '3 Month - 2 Connections', price: 30, duration: '3 months', connections: 2 },
    '6month_2': { name: '6 Month - 2 Connections', price: 55, duration: '6 months', connections: 2 },
    '12month_2': { name: '12 Month - 2 Connections', price: 85, duration: '12 months', connections: 2 },

    // 3 Connections Plans
    '1month_3': { name: '1 Month - 3 Connections', price: 20, duration: '1 month', connections: 3 },
    '3month_3': { name: '3 Month - 3 Connections', price: 40, duration: '3 months', connections: 3 },
    '6month_3': { name: '6 Month - 3 Connections', price: 70, duration: '6 months', connections: 3 },
    '12month_3': { name: '12 Month - 3 Connections', price: 110, duration: '12 months', connections: 3 },

    // 8 Connections Plans
    '1month_8': { name: '1 Month - Up to 8 Connections', price: 35, duration: '1 month', connections: 8 },
    '3month_8': { name: '3 Month - Up to 8 Connections', price: 75, duration: '3 months', connections: 8 },
    '6month_8': { name: '6 Month - Up to 8 Connections', price: 135, duration: '6 months', connections: 8 },
    '12month_8': { name: '12 Month - Up to 8 Connections', price: 200, duration: '12 months', connections: 8 }
};

// Stripe configuration
let stripe;
let elements;
let cardElement;

// PayPal configuration
let paypalButtons;

document.addEventListener('DOMContentLoaded', function() {
    initializeCheckout();
    initializePaymentMethods();
    loadPlanFromURL();
});

function initializeCheckout() {
    // Initialize Stripe
    stripe = Stripe('pk_test_YOUR_STRIPE_PUBLISHABLE_KEY'); // Replace with your actual key
    elements = stripe.elements();
    
    // Create card element
    cardElement = elements.create('card', {
        style: {
            base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': {
                    color: '#aab7c4',
                },
            },
        },
    });
    
    cardElement.mount('#card-element');
    
    // Handle real-time validation errors from the card Element
    cardElement.on('change', function(event) {
        const displayError = document.getElementById('card-errors');
        if (event.error) {
            displayError.textContent = event.error.message;
        } else {
            displayError.textContent = '';
        }
    });
}

function initializePaymentMethods() {
    const paymentMethods = document.querySelectorAll('.payment-method');
    const paymentForms = document.querySelectorAll('.payment-form');
    
    paymentMethods.forEach(method => {
        method.addEventListener('click', function() {
            const selectedMethod = this.dataset.method;
            
            // Update active states
            paymentMethods.forEach(m => m.classList.remove('active'));
            paymentForms.forEach(f => f.classList.remove('active'));
            
            this.classList.add('active');
            document.getElementById(`${selectedMethod}-payment`).classList.add('active');
            
            // Initialize specific payment method
            if (selectedMethod === 'paypal') {
                initializePayPal();
            }
        });
    });
    
    // Initialize default payment method (PayPal)
    initializePayPal();
    
    // Stripe submit handler
    document.getElementById('stripe-submit').addEventListener('click', handleStripePayment);
    
    // Crypto submit handler
    document.getElementById('crypto-submit').addEventListener('click', handleCryptoPayment);
}

function loadPlanFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    const planType = urlParams.get('plan');
    const connections = urlParams.get('connections');
    const price = urlParams.get('price');

    // Create plan key from plan type and connections
    const planKey = planType && connections ? `${planType}_${connections}` : null;

    if (planKey && plans[planKey]) {
        const plan = plans[planKey];
        updateOrderSummary(plan);
    } else if (price && connections) {
        // Fallback if plan type not found but price and connections are provided
        const customPlan = {
            name: `IPTV Subscription - ${connections} Connection${connections > 1 ? 's' : ''}`,
            price: parseFloat(price),
            duration: planType || 'Custom',
            connections: parseInt(connections)
        };
        updateOrderSummary(customPlan);
    } else if (price) {
        // Basic fallback with just price
        const customPlan = {
            name: 'IPTV Subscription',
            price: parseFloat(price),
            duration: 'Custom',
            connections: 2
        };
        updateOrderSummary(customPlan);
    } else {
        // Default to 1 month, 2 connections plan
        updateOrderSummary(plans['1month_2']);
    }
}

function updateOrderSummary(plan) {
    document.getElementById('planName').textContent = plan.name;
    
    const subtotal = plan.price;
    const tax = 0; // No tax for digital services
    const total = subtotal + tax;
    
    document.getElementById('subtotal').textContent = `$${subtotal.toFixed(2)}`;
    document.getElementById('tax').textContent = `$${tax.toFixed(2)}`;
    document.getElementById('total').textContent = `$${total.toFixed(2)}`;
    
    // Store plan data for payment processing
    window.currentPlan = plan;
    window.orderTotal = total;
}

function initializePayPal() {
    // Clear existing PayPal buttons
    const container = document.getElementById('paypal-button-container');
    container.innerHTML = '';
    
    if (typeof paypal !== 'undefined') {
        paypal.Buttons({
            createOrder: function(data, actions) {
                return actions.order.create({
                    purchase_units: [{
                        amount: {
                            value: window.orderTotal.toFixed(2)
                        },
                        description: `BadBoyz IPTV - ${window.currentPlan.name} - ${window.currentPlan.connections} streams`
                    }]
                });
            },
            onApprove: function(data, actions) {
                return actions.order.capture().then(function(details) {
                    handlePaymentSuccess('paypal', details);
                });
            },
            onError: function(err) {
                handlePaymentError('PayPal payment failed: ' + err);
            }
        }).render('#paypal-button-container');
    }
}

async function handleStripePayment(event) {
    event.preventDefault();
    
    if (!validateCustomerForm()) {
        return;
    }
    
    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    
    try {
        // Create payment intent on your server
        const response = await fetch('/api/create-payment-intent', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                amount: Math.round(window.orderTotal * 100), // Convert to cents
                currency: 'usd',
                plan: window.currentPlan,
                customer: getCustomerData()
            }),
        });
        
        const { client_secret } = await response.json();
        
        // Confirm payment with Stripe
        const result = await stripe.confirmCardPayment(client_secret, {
            payment_method: {
                card: cardElement,
                billing_details: {
                    name: `${document.getElementById('firstName').value} ${document.getElementById('lastName').value}`,
                    email: document.getElementById('email').value,
                }
            }
        });
        
        if (result.error) {
            handlePaymentError(result.error.message);
        } else {
            handlePaymentSuccess('stripe', result.paymentIntent);
        }
    } catch (error) {
        handlePaymentError('Payment processing failed. Please try again.');
    } finally {
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-lock"></i> Pay Securely';
    }
}

async function handleCryptoPayment(event) {
    event.preventDefault();
    
    if (!validateCustomerForm()) {
        return;
    }
    
    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    
    try {
        // Send crypto payment request to server
        const response = await fetch('/api/crypto-payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                amount: window.orderTotal,
                plan: window.currentPlan,
                customer: getCustomerData()
            }),
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Redirect to crypto payment instructions page
            window.location.href = `crypto-instructions.html?order=${result.orderId}`;
        } else {
            handlePaymentError('Failed to create crypto payment. Please try again.');
        }
    } catch (error) {
        handlePaymentError('Payment processing failed. Please try again.');
    } finally {
        button.disabled = false;
        button.innerHTML = '<i class="fab fa-bitcoin"></i> Pay with Crypto';
    }
}

function validateCustomerForm() {
    const form = document.getElementById('customerForm');
    const termsCheckbox = document.getElementById('terms');
    
    if (!form.checkValidity()) {
        form.reportValidity();
        return false;
    }
    
    if (!termsCheckbox.checked) {
        alert('Please accept the Terms of Service and Privacy Policy to continue.');
        return false;
    }
    
    return true;
}

function getCustomerData() {
    return {
        firstName: document.getElementById('firstName').value,
        lastName: document.getElementById('lastName').value,
        email: document.getElementById('email').value,
        phone: document.getElementById('phone').value,
        country: document.getElementById('country').value,
        newsletter: document.getElementById('newsletter').checked
    };
}

function handlePaymentSuccess(method, paymentData) {
    // Store payment data for success page
    sessionStorage.setItem('paymentSuccess', JSON.stringify({
        method: method,
        plan: window.currentPlan,
        customer: getCustomerData(),
        paymentData: paymentData,
        timestamp: new Date().toISOString()
    }));
    
    // Redirect to success page
    window.location.href = 'payment-success.html';
}

function handlePaymentError(message) {
    // Show error notification
    showNotification(message, 'error');
    
    // Log error for debugging
    console.error('Payment error:', message);
}

// Utility function for notifications (if not already defined in main.js)
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 10000;
        animation: slideIn 0.3s ease;
        max-width: 300px;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 5000);
}
