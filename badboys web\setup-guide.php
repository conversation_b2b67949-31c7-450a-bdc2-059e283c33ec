<?php
// BadBoyz IPTV - Setup Guide
// This file helps you configure your database and initial settings

$setupComplete = false;
$errors = [];
$success = [];

// Check if setup is being submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_submit'])) {
    $dbHost = trim($_POST['db_host'] ?? 'localhost');
    $dbName = trim($_POST['db_name'] ?? '');
    $dbUser = trim($_POST['db_user'] ?? '');
    $dbPass = $_POST['db_pass'] ?? '';
    $adminEmail = trim($_POST['admin_email'] ?? '');
    $siteUrl = trim($_POST['site_url'] ?? '');
    
    // Validate inputs
    if (empty($dbName)) $errors[] = 'Database name is required';
    if (empty($dbUser)) $errors[] = 'Database username is required';
    if (empty($adminEmail)) $errors[] = 'Admin email is required';
    if (empty($siteUrl)) $errors[] = 'Site URL is required';
    
    if (empty($errors)) {
        // Test database connection
        try {
            $dsn = "mysql:host={$dbHost};charset=utf8mb4";
            $pdo = new PDO($dsn, $dbUser, $dbPass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $success[] = "Database '{$dbName}' created successfully";
            
            // Test connection to the specific database
            $dsn = "mysql:host={$dbHost};dbname={$dbName};charset=utf8mb4";
            $pdo = new PDO($dsn, $dbUser, $dbPass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            $success[] = "Database connection successful";
            
            // Update config file
            $configContent = file_get_contents('config/config.php');
            
            $configContent = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '{$dbHost}');", $configContent);
            $configContent = str_replace("define('DB_NAME', 'badboyz_iptv');", "define('DB_NAME', '{$dbName}');", $configContent);
            $configContent = str_replace("define('DB_USER', 'your_db_user');", "define('DB_USER', '{$dbUser}');", $configContent);
            $configContent = str_replace("define('DB_PASS', 'your_db_password');", "define('DB_PASS', '{$dbPass}');", $configContent);
            $configContent = str_replace("define('APP_URL', 'https://your-domain.com');", "define('APP_URL', '{$siteUrl}');", $configContent);
            $configContent = str_replace("define('ADMIN_EMAIL', '<EMAIL>');", "define('ADMIN_EMAIL', '{$adminEmail}');", $configContent);
            
            if (file_put_contents('config/config.php', $configContent)) {
                $success[] = "Configuration file updated successfully";
                
                // Test the full system
                require_once 'config/config.php';
                require_once 'config/database.php';
                
                $success[] = "Database tables created successfully";
                $success[] = "Default admin user created (username: admin, password: admin123)";
                $setupComplete = true;
            } else {
                $errors[] = "Failed to update configuration file. Please check file permissions.";
            }
            
        } catch (PDOException $e) {
            $errors[] = "Database connection failed: " . $e->getMessage();
        } catch (Exception $e) {
            $errors[] = "Setup failed: " . $e->getMessage();
        }
    }
}

// Get current config values if they exist
$currentConfig = [];
if (file_exists('config/config.php')) {
    $configContent = file_get_contents('config/config.php');
    preg_match("/define\('DB_HOST', '([^']+)'\)/", $configContent, $matches);
    $currentConfig['db_host'] = $matches[1] ?? 'localhost';
    
    preg_match("/define\('DB_NAME', '([^']+)'\)/", $configContent, $matches);
    $currentConfig['db_name'] = $matches[1] ?? 'badboyz_iptv';
    
    preg_match("/define\('DB_USER', '([^']+)'\)/", $configContent, $matches);
    $currentConfig['db_user'] = $matches[1] ?? '';
    
    preg_match("/define\('APP_URL', '([^']+)'\)/", $configContent, $matches);
    $currentConfig['site_url'] = $matches[1] ?? '';
    
    preg_match("/define\('ADMIN_EMAIL', '([^']+)'\)/", $configContent, $matches);
    $currentConfig['admin_email'] = $matches[1] ?? '';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BadBoyz IPTV - Setup Guide</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .setup-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .setup-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .setup-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .setup-header p {
            color: #666;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #007bff;
            color: white;
        }
        .step.complete {
            background: #28a745;
            color: white;
        }
        .form-section {
            margin-bottom: 30px;
        }
        .form-section h4 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }
        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .setup-complete {
            text-align: center;
            padding: 40px 20px;
        }
        .setup-complete i {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="setup-header">
                <h1><i class="fas fa-cog"></i> BadBoyz IPTV Setup</h1>
                <p>Configure your database and initial settings</p>
            </div>

            <div class="step-indicator">
                <div class="step <?php echo !$setupComplete ? 'active' : 'complete'; ?>">1</div>
                <div class="step <?php echo $setupComplete ? 'complete' : ''; ?>">2</div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="error-message">
                    <h5><i class="fas fa-exclamation-triangle"></i> Setup Errors:</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="success-message">
                    <h5><i class="fas fa-check-circle"></i> Success:</h5>
                    <ul class="mb-0">
                        <?php foreach ($success as $msg): ?>
                            <li><?php echo htmlspecialchars($msg); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if ($setupComplete): ?>
                <div class="setup-complete">
                    <i class="fas fa-check-circle"></i>
                    <h2>Setup Complete!</h2>
                    <p>Your BadBoyz IPTV website has been configured successfully.</p>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5>Admin Login:</h5>
                            <p><strong>Username:</strong> admin<br>
                            <strong>Password:</strong> admin123</p>
                        </div>
                        <div class="col-md-6">
                            <h5>Next Steps:</h5>
                            <p>1. Change admin password<br>
                            2. Configure email settings<br>
                            3. Customize your content</p>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-home"></i> Visit Website
                        </a>
                        <a href="admin/index.php" class="btn btn-success btn-lg">
                            <i class="fas fa-cog"></i> Admin Panel
                        </a>
                    </div>
                    
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i> 
                            You can delete this setup-guide.php file after setup is complete.
                        </small>
                    </div>
                </div>
            <?php else: ?>
                <form method="POST">
                    <div class="form-section">
                        <h4><i class="fas fa-database"></i> Database Configuration</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="db_host" class="form-label">Database Host</label>
                                <input type="text" class="form-control" id="db_host" name="db_host" 
                                       value="<?php echo htmlspecialchars($currentConfig['db_host'] ?? 'localhost'); ?>" required>
                                <small class="form-text text-muted">Usually 'localhost' for most hosting providers</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="db_name" class="form-label">Database Name</label>
                                <input type="text" class="form-control" id="db_name" name="db_name" 
                                       value="<?php echo htmlspecialchars($currentConfig['db_name'] ?? 'badboyz_iptv'); ?>" required>
                                <small class="form-text text-muted">Will be created if it doesn't exist</small>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="db_user" class="form-label">Database Username</label>
                                <input type="text" class="form-control" id="db_user" name="db_user" 
                                       value="<?php echo htmlspecialchars($currentConfig['db_user'] ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="db_pass" class="form-label">Database Password</label>
                                <input type="password" class="form-control" id="db_pass" name="db_pass">
                                <small class="form-text text-muted">Leave blank if no password</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4><i class="fas fa-globe"></i> Site Configuration</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="site_url" class="form-label">Site URL</label>
                                <input type="url" class="form-control" id="site_url" name="site_url" 
                                       value="<?php echo htmlspecialchars($currentConfig['site_url'] ?? 'https://'); ?>" required>
                                <small class="form-text text-muted">Your website URL (e.g., https://yourdomain.com)</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="admin_email" class="form-label">Admin Email</label>
                                <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                       value="<?php echo htmlspecialchars($currentConfig['admin_email'] ?? ''); ?>" required>
                                <small class="form-text text-muted">Email for admin notifications</small>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" name="setup_submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-rocket"></i> Complete Setup
                        </button>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
