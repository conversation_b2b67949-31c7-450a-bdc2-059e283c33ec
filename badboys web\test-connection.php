<?php
// InMotion Database Connection Test
// Upload this file and visit it to test your database connection

echo "<h1>🔧 InMotion Database Connection Test</h1>";

// Check if config exists
if (!file_exists('config/config.php')) {
    echo "<p style='color: red;'>❌ Config file not found!</p>";
    echo "<p>Make sure you uploaded all files to your public_html folder.</p>";
    exit;
}

// Load config
require_once 'config/config.php';

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📋 Current Configuration:</h2>";
echo "<p><strong>Database Host:</strong> " . DB_HOST . "</p>";
echo "<p><strong>Database Name:</strong> " . DB_NAME . "</p>";
echo "<p><strong>Database User:</strong> " . DB_USER . "</p>";
echo "<p><strong>Password:</strong> " . (DB_PASS ? '[SET - ' . substr(DB_PASS, 0, 3) . '***]' : '[NOT SET]') . "</p>";
echo "</div>";

// Check for placeholder values
$hasPlaceholders = false;
if (strpos(DB_NAME, 'your_cpanel_username') !== false) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠️ Configuration Needed</h3>";
    echo "<p>You need to update your database configuration in <code>config/config.php</code></p>";
    echo "<p><strong>Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Go to your InMotion cPanel</li>";
    echo "<li>Create a MySQL database (e.g., 'badboyz_iptv')</li>";
    echo "<li>Create a database user and add to database</li>";
    echo "<li>Update the config file with your actual details</li>";
    echo "</ol>";
    echo "</div>";
    $hasPlaceholders = true;
}

if (!$hasPlaceholders) {
    echo "<h2>🔍 Testing Connection...</h2>";
    
    try {
        // Test connection
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ Connection Successful!</h3>";
        echo "<p>Your database connection is working perfectly.</p>";
        
        // Check if tables exist
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "<p>📝 Database is empty - tables will be created automatically when you visit your website.</p>";
        } else {
            echo "<p>📊 Found " . count($tables) . " tables: " . implode(', ', $tables) . "</p>";
        }
        echo "</div>";
        
        echo "<div style='text-align: center; margin: 30px 0;'>";
        echo "<a href='index.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>🚀 Go to Your Website</a>";
        echo "</div>";
        
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>🔐 Default Admin Login:</h4>";
        echo "<p><strong>URL:</strong> <a href='admin/'>admin/</a></p>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
        echo "<p><em>⚠️ Change this password immediately after login!</em></p>";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>❌ Connection Failed</h3>";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        
        echo "<h4>🔧 Common InMotion Solutions:</h4>";
        echo "<ul>";
        echo "<li><strong>Database Name Format:</strong> Make sure it includes your cPanel username prefix (e.g., 'john123_badboyz_iptv')</li>";
        echo "<li><strong>User Permissions:</strong> In cPanel > MySQL Databases, ensure your user has 'ALL PRIVILEGES'</li>";
        echo "<li><strong>Password:</strong> Double-check your database user password</li>";
        echo "<li><strong>Database Exists:</strong> Verify the database was created in cPanel</li>";
        echo "</ul>";
        
        echo "<h4>📞 Need Help?</h4>";
        echo "<p>Contact InMotion support - they can help verify your database setup.</p>";
        echo "</div>";
    }
}

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; font-size: 14px; color: #666;'>";
echo "<h4>📚 Quick InMotion Database Setup:</h4>";
echo "<ol>";
echo "<li>Login to cPanel</li>";
echo "<li>Go to 'MySQL Databases'</li>";
echo "<li>Create database: 'badboyz_iptv'</li>";
echo "<li>Create user: 'badboyz_user'</li>";
echo "<li>Add user to database with ALL PRIVILEGES</li>";
echo "<li>Update config/config.php with the full names (including your username prefix)</li>";
echo "</ol>";
echo "<p><strong>Remember:</strong> InMotion adds your cPanel username as a prefix to database names and users!</p>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0; padding: 20px; background: #fff3cd; border-radius: 5px;'>";
echo "<p><strong>🗑️ Delete this file after testing!</strong></p>";
echo "<p>For security, remove 'test-connection.php' once your database is working.</p>";
echo "</div>";
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h1, h2, h3, h4 {
    color: #333;
}

code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul, ol {
    padding-left: 20px;
}

li {
    margin: 5px 0;
}
</style>
