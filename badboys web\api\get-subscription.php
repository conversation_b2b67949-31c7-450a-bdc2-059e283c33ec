<?php
header('Content-Type: application/json');

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Verify CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

$subscriptionId = intval($_POST['subscription_id'] ?? 0);
$userId = $_SESSION['user_id'];

if (!$subscriptionId) {
    echo json_encode(['success' => false, 'message' => 'Invalid subscription ID']);
    exit;
}

// Get subscription details (ensure it belongs to the current user)
$subscription = fetchOne("SELECT * FROM subscriptions WHERE id = ? AND user_id = ?", [$subscriptionId, $userId]);

if (!$subscription) {
    echo json_encode(['success' => false, 'message' => 'Subscription not found']);
    exit;
}

// Generate HTML for subscription details
$html = '
<div class="subscription-details">
    <div class="row">
        <div class="col-md-6">
            <h5>Subscription Information</h5>
            <table class="table table-borderless">
                <tr>
                    <td><strong>Plan:</strong></td>
                    <td>' . ucfirst($subscription['plan_type']) . ' Plan</td>
                </tr>
                <tr>
                    <td><strong>Connections:</strong></td>
                    <td>' . $subscription['connections'] . ' simultaneous streams</td>
                </tr>
                <tr>
                    <td><strong>Price:</strong></td>
                    <td>' . formatPrice($subscription['price']) . '/month</td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td><span class="badge bg-' . ($subscription['status'] === 'active' ? 'success' : ($subscription['status'] === 'expired' ? 'danger' : 'warning')) . '">' . ucfirst($subscription['status']) . '</span></td>
                </tr>
                <tr>
                    <td><strong>Start Date:</strong></td>
                    <td>' . date('M j, Y', strtotime($subscription['start_date'])) . '</td>
                </tr>
                <tr>
                    <td><strong>End Date:</strong></td>
                    <td>' . date('M j, Y', strtotime($subscription['end_date'])) . '</td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h5>IPTV Credentials</h5>
            <div class="credentials-box">
                <div class="credential-item">
                    <label>Server URL:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" value="' . htmlspecialchars($subscription['server_url']) . '" readonly>
                        <button class="btn btn-outline-secondary" onclick="copyToClipboard(this.previousElementSibling)">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <div class="credential-item">
                    <label>Username:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" value="' . htmlspecialchars($subscription['username']) . '" readonly>
                        <button class="btn btn-outline-secondary" onclick="copyToClipboard(this.previousElementSibling)">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <div class="credential-item">
                    <label>Password:</label>
                    <div class="input-group">
                        <input type="password" class="form-control" value="' . htmlspecialchars($subscription['password']) . '" readonly id="password-' . $subscription['id'] . '">
                        <button class="btn btn-outline-secondary" onclick="togglePasswordVisibility(\'password-' . $subscription['id'] . '\')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="copyToClipboard(document.getElementById(\'password-' . $subscription['id'] . '\'))">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <h5>Setup Instructions</h5>
            <div class="setup-instructions">
                <div class="instruction-step">
                    <span class="step-number">1</span>
                    <div class="step-content">
                        <h6>Download IPTV Player</h6>
                        <p>Download an IPTV player app like IPTV Smarters, Perfect Player, or VLC Media Player on your device.</p>
                    </div>
                </div>
                <div class="instruction-step">
                    <span class="step-number">2</span>
                    <div class="step-content">
                        <h6>Enter Credentials</h6>
                        <p>Open the app and enter the server URL, username, and password provided above.</p>
                    </div>
                </div>
                <div class="instruction-step">
                    <span class="step-number">3</span>
                    <div class="step-content">
                        <h6>Start Streaming</h6>
                        <p>Once connected, you can browse and watch 16,000+ channels and 40,000+ VOD content.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="action-buttons">
                <a href="tutorials.php" class="btn btn-info" target="_blank">
                    <i class="fas fa-book"></i> Setup Tutorial
                </a>
                <button class="btn btn-success" onclick="renewSubscription(' . $subscription['id'] . ')">
                    <i class="fas fa-redo"></i> Renew Subscription
                </button>
                <a href="contact.php" class="btn btn-outline-primary">
                    <i class="fas fa-headset"></i> Get Support
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.credentials-box {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

.credential-item {
    margin-bottom: 15px;
}

.credential-item label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}

.setup-instructions {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
}

.instruction-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.instruction-step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 30px;
    height: 30px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-content h6 {
    margin-bottom: 5px;
    color: #333;
}

.step-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        width: 100%;
    }
}
</style>

<script>
function copyToClipboard(element) {
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand("copy");
    showNotification("Copied to clipboard!", "success", 2000);
}

function togglePasswordVisibility(elementId) {
    const element = document.getElementById(elementId);
    const button = element.nextElementSibling;
    const icon = button.querySelector("i");
    
    if (element.type === "password") {
        element.type = "text";
        icon.classList.remove("fa-eye");
        icon.classList.add("fa-eye-slash");
    } else {
        element.type = "password";
        icon.classList.remove("fa-eye-slash");
        icon.classList.add("fa-eye");
    }
}

function renewSubscription(subscriptionId) {
    if (confirm("Are you sure you want to renew this subscription?")) {
        window.location.href = "checkout.php?renew=" + subscriptionId;
    }
}
</script>';

echo json_encode(['success' => true, 'html' => $html]);
?>
