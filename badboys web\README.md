# BadBoyz IPTV - Complete PHP Website

## 🚀 Features Implemented

### ✅ **Core Website Features**
- **Responsive Design**: Mobile-first, Bootstrap 5 powered
- **Dynamic PHP Pages**: All HTML converted to PHP with includes
- **SEO Optimized**: Meta tags, structured data, clean URLs
- **Performance Optimized**: Minified assets, optimized images

### ✅ **User Management System**
- **User Registration & Login**: Secure authentication system
- **Password Security**: Bcrypt hashing, strength validation
- **Session Management**: Secure sessions with CSRF protection
- **User Roles**: Customer, Reseller, Admin roles
- **Account Lockout**: Brute force protection

### ✅ **Subscription Management**
- **Dynamic Pricing Plans**: Basic, Pro, Premium plans
- **IPTV Credentials**: Auto-generated server credentials
- **Subscription Tracking**: Start/end dates, status management
- **Renewal System**: Automatic and manual renewal options

### ✅ **Free Trial System**
- **24-Hour Trials**: Automated trial account creation
- **IP Restrictions**: Prevent abuse with IP tracking
- **Email Delivery**: Automated credential delivery
- **Trial Management**: Admin oversight of trial usage

### ✅ **Support System**
- **Ticket System**: Full support ticket management
- **Live Chat**: Real-time customer support
- **Priority Levels**: Low, Medium, High, Urgent
- **Email Notifications**: Automated responses

### ✅ **Admin Panel**
- **Dashboard**: Revenue charts, user statistics
- **User Management**: View, edit, suspend users
- **Subscription Management**: Create, modify subscriptions
- **Support Tickets**: Manage customer inquiries
- **Analytics**: Revenue tracking, user activity
- **System Settings**: Configure site-wide settings

### ✅ **Client Dashboard**
- **Account Overview**: Subscription status, usage stats
- **Credential Access**: Copy/paste IPTV credentials
- **Support History**: View ticket history
- **Profile Management**: Update account information

### ✅ **Security Features**
- **CSRF Protection**: All forms protected
- **Input Validation**: Sanitized user inputs
- **SQL Injection Prevention**: Prepared statements
- **XSS Protection**: Output escaping
- **Secure Sessions**: HTTPOnly, Secure cookies

### ✅ **Database Structure**
- **Users Table**: Complete user management
- **Subscriptions Table**: IPTV subscription tracking
- **Trials Table**: Free trial management
- **Support Tickets**: Customer support system
- **Payments Table**: Transaction tracking
- **Analytics Table**: Site usage tracking

## 📁 Clean File Structure

```
badboyz-iptv/
├── config/
│   ├── config.php          # Main configuration
│   └── database.php        # Database connection & functions
├── includes/
│   ├── functions.php       # Core PHP functions
│   ├── header.php         # Site header template
│   └── footer.php         # Site footer template
├── admin/
│   └── index.php          # Admin panel dashboard
├── api/
│   └── get-subscription.php # AJAX endpoints
├── assets/
│   ├── css/
│   │   └── style.css      # Main stylesheet
│   ├── js/
│   │   ├── main.js        # Enhanced main JavaScript
│   │   └── livechat.js    # Live chat functionality
│   └── images/
│       ├── favicon.svg    # Site favicon
│       └── hero-tv.png    # Hero image
├── index.php              # Homepage
├── login.php              # User login
├── register.php           # User registration
├── logout.php             # User logout
├── client-area.php        # User dashboard
├── free-trial.php         # Free trial request
├── contact.php            # Contact form & support
├── subscriptions.php      # Subscription plans
├── about.php              # About page
├── tutorials.php          # Setup tutorials
├── reseller.php           # Reseller program
├── checkout.php           # Payment & checkout
└── README.md              # Documentation
```

**✅ All old HTML files removed - Only working PHP files remain!**

## 🛠 Installation Instructions

### 1. **Server Requirements**
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache/Nginx web server
- mod_rewrite enabled

### 2. **Database Setup**
1. Create a MySQL database
2. Update database credentials in `config/config.php`
3. Tables will be created automatically on first run

### 3. **Configuration**
1. Edit `config/config.php`:
   - Update database credentials
   - Set your domain URL
   - Configure email settings
   - Set IPTV server details

### 4. **File Permissions**
```bash
chmod 755 assets/
chmod 644 assets/css/*
chmod 644 assets/js/*
chmod 644 assets/images/*
```

### 5. **Default Admin Account**
- **Username**: admin
- **Email**: Your admin email (set in config)
- **Password**: admin123 (change immediately!)

## 🔧 Configuration Options

### Email Settings
```php
define('SMTP_HOST', 'smtp.your-hosting.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-email-password');
```

### IPTV Servers
```php
$iptv_servers = [
    'main' => [
        'name' => 'Main Server',
        'url' => 'http://server.badboyz-iptv.com:8080',
        'location' => 'USA',
        'status' => 'active'
    ]
];
```

### Subscription Plans
```php
$subscription_plans = [
    'basic' => [
        'name' => 'Basic Plan',
        'price' => 25,
        'connections' => 2,
        'features' => [...]
    ]
];
```

## 🎨 Customization

### Branding
- Update `APP_NAME` in `config/config.php`
- Replace logo in `includes/header.php`
- Modify colors in `assets/css/style.css`

### Features
- Enable/disable trials: `$site_settings['trial_enabled']`
- Enable/disable reseller program: `$site_settings['reseller_program']`
- Enable/disable live chat: `$site_settings['live_chat_enabled']`

## 🔒 Security Best Practices

1. **Change default admin password**
2. **Update all configuration settings**
3. **Enable HTTPS in production**
4. **Regular database backups**
5. **Keep PHP and MySQL updated**
6. **Monitor error logs**

## 📊 Analytics Integration

- **Google Analytics**: Set ID in config
- **Facebook Pixel**: Set ID in config
- **Custom Analytics**: Built-in page tracking

## 🎯 SEO Features

- **Meta Tags**: Dynamic titles and descriptions
- **Structured Data**: Rich snippets ready
- **Clean URLs**: SEO-friendly structure
- **Sitemap Ready**: Easy sitemap generation

## 🚀 Performance Features

- **Optimized Database**: Indexed queries
- **Cached Assets**: Browser caching headers
- **Minified Code**: Compressed CSS/JS
- **Image Optimization**: WebP support ready

## 📱 Mobile Features

- **Responsive Design**: Works on all devices
- **Touch Friendly**: Mobile-optimized interface
- **Fast Loading**: Optimized for mobile networks
- **PWA Ready**: Progressive Web App features

## 🔧 Troubleshooting

### Common Issues
1. **Database Connection**: Check credentials in config
2. **Email Not Sending**: Verify SMTP settings
3. **Permissions**: Ensure proper file permissions
4. **Sessions**: Check PHP session configuration

### Debug Mode
Enable debug mode in `config/config.php`:
```php
define('DEVELOPMENT', true);
```

## 📞 Support

For technical support or customization requests:
- Email: <EMAIL>
- Documentation: Available in admin panel
- Updates: Check for updates regularly

---

**Built with ❤️ for BadBoyz IPTV**
*Professional IPTV Website Solution*
