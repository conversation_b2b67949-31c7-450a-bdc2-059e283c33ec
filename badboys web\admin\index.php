<?php
$pageTitle = "Admin Panel - Dashboard";
$pageDescription = "BadBoyz IPTV Admin Panel - Manage customers, subscriptions, resellers, and system settings.";

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin access
requireAdmin();

// Get dashboard statistics
$totalUsers = fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'customer'")['count'];
$totalSubscriptions = fetchOne("SELECT COUNT(*) as count FROM subscriptions WHERE status = 'active'")['count'];
$totalRevenue = fetchOne("SELECT SUM(price) as total FROM subscriptions WHERE status = 'active'")['total'] ?? 0;
$totalTickets = fetchOne("SELECT COUNT(*) as count FROM support_tickets WHERE status IN ('open', 'in_progress')")['count'];

// Get recent activities
$recentUsers = fetchAll("SELECT * FROM users WHERE user_type = 'customer' OR<PERSON><PERSON> BY created_at DESC LIMIT 5");
$recentSubscriptions = fetchAll("SELECT s.*, u.username, u.email FROM subscriptions s JOIN users u ON s.user_id = u.id ORDER BY s.created_at DESC LIMIT 5");
$recentTickets = fetchAll("SELECT * FROM support_tickets ORDER BY created_at DESC LIMIT 5");

// Get monthly revenue data for chart
$monthlyRevenue = fetchAll("
    SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        SUM(price) as revenue,
        COUNT(*) as subscriptions
    FROM subscriptions 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
    ORDER BY month ASC
");

require_once '../includes/header.php';
?>

<div class="admin-panel">
    <!-- Sidebar -->
    <div class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-cog"></i> Admin Panel</h4>
            <button class="sidebar-toggle d-lg-none" onclick="toggleSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="#" class="nav-link active" data-section="dashboard">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a href="#" class="nav-link" data-section="users">
                <i class="fas fa-users"></i> Users
            </a>
            <a href="#" class="nav-link" data-section="subscriptions">
                <i class="fas fa-tv"></i> Subscriptions
            </a>
            <a href="#" class="nav-link" data-section="tickets">
                <i class="fas fa-headset"></i> Support Tickets
            </a>
            <a href="#" class="nav-link" data-section="trials">
                <i class="fas fa-play"></i> Trials
            </a>
            <a href="#" class="nav-link" data-section="payments">
                <i class="fas fa-credit-card"></i> Payments
            </a>
            <a href="#" class="nav-link" data-section="analytics">
                <i class="fas fa-chart-bar"></i> Analytics
            </a>
            <a href="#" class="nav-link" data-section="settings">
                <i class="fas fa-cog"></i> Settings
            </a>
            <div class="nav-divider"></div>
            <a href="../index.php" class="nav-link">
                <i class="fas fa-home"></i> Back to Site
            </a>
            <a href="../logout.php" class="nav-link">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="admin-content">
        <!-- Header -->
        <div class="admin-header">
            <button class="sidebar-toggle d-lg-none" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
            <h1>Admin Dashboard</h1>
            <div class="admin-user">
                <i class="fas fa-user-shield"></i>
                <span><?php echo htmlspecialchars($_SESSION['username']); ?></span>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div class="admin-section" id="dashboard-section">
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card bg-primary">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($totalUsers); ?></h3>
                            <p>Total Users</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card bg-success">
                        <div class="stat-icon">
                            <i class="fas fa-tv"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($totalSubscriptions); ?></h3>
                            <p>Active Subscriptions</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card bg-info">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo formatPrice($totalRevenue); ?></h3>
                            <p>Monthly Revenue</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card bg-warning">
                        <div class="stat-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($totalTickets); ?></h3>
                            <p>Open Tickets</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts and Recent Activity -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="admin-card">
                        <div class="card-header">
                            <h4>Revenue Overview</h4>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="admin-card">
                        <div class="card-header">
                            <h4>Quick Actions</h4>
                        </div>
                        <div class="card-body">
                            <div class="quick-actions">
                                <button class="btn btn-primary btn-sm w-100 mb-2" onclick="showSection('users')">
                                    <i class="fas fa-user-plus"></i> Add New User
                                </button>
                                <button class="btn btn-success btn-sm w-100 mb-2" onclick="showSection('subscriptions')">
                                    <i class="fas fa-plus"></i> Create Subscription
                                </button>
                                <button class="btn btn-info btn-sm w-100 mb-2" onclick="showSection('tickets')">
                                    <i class="fas fa-headset"></i> View Tickets
                                </button>
                                <button class="btn btn-warning btn-sm w-100" onclick="showSection('settings')">
                                    <i class="fas fa-cog"></i> System Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row mt-4">
                <div class="col-lg-4">
                    <div class="admin-card">
                        <div class="card-header">
                            <h5>Recent Users</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($recentUsers as $user): ?>
                            <div class="activity-item">
                                <div class="activity-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="activity-content">
                                    <h6><?php echo htmlspecialchars($user['username']); ?></h6>
                                    <small><?php echo timeAgo($user['created_at']); ?></small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="admin-card">
                        <div class="card-header">
                            <h5>Recent Subscriptions</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($recentSubscriptions as $sub): ?>
                            <div class="activity-item">
                                <div class="activity-avatar bg-success">
                                    <i class="fas fa-tv"></i>
                                </div>
                                <div class="activity-content">
                                    <h6><?php echo ucfirst($sub['plan_type']); ?> - <?php echo htmlspecialchars($sub['username']); ?></h6>
                                    <small><?php echo formatPrice($sub['price']); ?> - <?php echo timeAgo($sub['created_at']); ?></small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="admin-card">
                        <div class="card-header">
                            <h5>Recent Tickets</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($recentTickets as $ticket): ?>
                            <div class="activity-item">
                                <div class="activity-avatar bg-info">
                                    <i class="fas fa-headset"></i>
                                </div>
                                <div class="activity-content">
                                    <h6><?php echo htmlspecialchars(substr($ticket['subject'], 0, 30)) . '...'; ?></h6>
                                    <small><?php echo htmlspecialchars($ticket['name']); ?> - <?php echo timeAgo($ticket['created_at']); ?></small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Other sections will be loaded via AJAX -->
        <div class="admin-section" id="users-section" style="display: none;">
            <div class="section-loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>Loading users...</p>
            </div>
        </div>

        <div class="admin-section" id="subscriptions-section" style="display: none;">
            <div class="section-loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>Loading subscriptions...</p>
            </div>
        </div>

        <div class="admin-section" id="tickets-section" style="display: none;">
            <div class="section-loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>Loading support tickets...</p>
            </div>
        </div>

        <div class="admin-section" id="trials-section" style="display: none;">
            <div class="section-loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>Loading trials...</p>
            </div>
        </div>

        <div class="admin-section" id="payments-section" style="display: none;">
            <div class="section-loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>Loading payments...</p>
            </div>
        </div>

        <div class="admin-section" id="analytics-section" style="display: none;">
            <div class="section-loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>Loading analytics...</p>
            </div>
        </div>

        <div class="admin-section" id="settings-section" style="display: none;">
            <div class="section-loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>Loading settings...</p>
            </div>
        </div>
    </div>
</div>

<style>
.admin-panel {
    display: flex;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-sidebar {
    width: 250px;
    background: #2c3e50;
    color: white;
    position: fixed;
    height: 100vh;
    left: -250px;
    transition: left 0.3s ease;
    z-index: 1000;
}

.admin-sidebar.show {
    left: 0;
}

@media (min-width: 992px) {
    .admin-sidebar {
        position: relative;
        left: 0;
    }
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #34495e;
    display: flex;
    justify-content: between;
    align-items: center;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background: #34495e;
    color: white;
}

.nav-link i {
    width: 20px;
    margin-right: 10px;
}

.nav-divider {
    height: 1px;
    background: #34495e;
    margin: 20px 0;
}

.admin-content {
    flex: 1;
    padding: 0;
    margin-left: 0;
}

@media (min-width: 992px) {
    .admin-content {
        margin-left: 250px;
    }
}

.admin-header {
    background: white;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: between;
}

.admin-header h1 {
    margin: 0;
    font-size: 1.5rem;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #666;
}

.admin-section {
    padding: 20px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    color: white;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stat-icon {
    font-size: 2rem;
    margin-right: 20px;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
}

.stat-content p {
    margin: 0;
    opacity: 0.9;
}

.admin-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.card-header h4,
.card-header h5 {
    margin: 0;
}

.card-body {
    padding: 20px;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-avatar {
    width: 40px;
    height: 40px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.activity-content h6 {
    margin: 0;
    font-size: 0.9rem;
}

.activity-content small {
    color: #666;
}

.section-loading {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.section-loading i {
    margin-bottom: 20px;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Admin Panel Functions
function toggleSidebar() {
    document.getElementById('adminSidebar').classList.toggle('show');
}

// Navigation
document.querySelectorAll('.nav-link[data-section]').forEach(link => {
    link.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Update active nav
        document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
        this.classList.add('active');
        
        // Show section
        const section = this.dataset.section;
        showSection(section);
    });
});

function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(s => s.style.display = 'none');
    
    // Show target section
    const targetSection = document.getElementById(sectionName + '-section');
    targetSection.style.display = 'block';
    
    // Load section content if not dashboard
    if (sectionName !== 'dashboard') {
        loadSectionContent(sectionName);
    }
}

function loadSectionContent(section) {
    const sectionElement = document.getElementById(section + '-section');
    
    // Show loading if content is not loaded
    if (sectionElement.querySelector('.section-loading')) {
        makeAjaxRequest(`admin-sections/${section}.php`, {}, function(response) {
            if (response.success) {
                sectionElement.innerHTML = response.html;
            } else {
                sectionElement.innerHTML = '<div class="alert alert-danger">Failed to load content: ' + response.message + '</div>';
            }
        });
    }
}

// Initialize revenue chart
const ctx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column($monthlyRevenue, 'month')); ?>,
        datasets: [{
            label: 'Revenue ($)',
            data: <?php echo json_encode(array_column($monthlyRevenue, 'revenue')); ?>,
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value;
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Close sidebar when clicking outside on mobile
document.addEventListener('click', function(e) {
    const sidebar = document.getElementById('adminSidebar');
    const toggleBtn = document.querySelector('.sidebar-toggle');
    
    if (window.innerWidth < 992 && !sidebar.contains(e.target) && !toggleBtn.contains(e.target)) {
        sidebar.classList.remove('show');
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
