<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Test - BadBoyz IPTV</title>
    
    <!-- Test CSS Loading -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://badboyzmedia.org/assets/css/style.css">
    
    <style>
        .test-section {
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        .status-good { color: #28a745; font-weight: bold; }
        .status-bad { color: #dc3545; font-weight: bold; }
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            color: #333;
        }
    </style>
</head>
<body>
    <div class="test-section">
        <div class="container">
            <h1><i class="fas fa-tv"></i> BadBoyz IPTV - CSS Test</h1>
            <p>Testing if all CSS and assets are loading correctly</p>
        </div>
    </div>

    <div class="container">
        <div class="test-card">
            <h2>🔍 CSS Loading Test</h2>
            
            <div class="row">
                <div class="col-md-4">
                    <h5>Bootstrap CSS</h5>
                    <p id="bootstrap-test" class="status-bad">❌ Not Loaded</p>
                </div>
                <div class="col-md-4">
                    <h5>Font Awesome</h5>
                    <p><i class="fas fa-check status-good"></i> <span id="fontawesome-test">✅ Loaded</span></p>
                </div>
                <div class="col-md-4">
                    <h5>Custom CSS</h5>
                    <p id="custom-css-test" class="status-bad">❌ Not Loaded</p>
                </div>
            </div>
            
            <hr>
            
            <h3>🎨 Style Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary btn-lg">Primary Button</button>
                    <button class="btn btn-success btn-lg">Success Button</button>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> This is a test alert
                    </div>
                </div>
            </div>
            
            <hr>
            
            <h3>📱 Responsive Test</h3>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-tv fa-3x text-primary mb-3"></i>
                            <h5>16,000+ Channels</h5>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-film fa-3x text-success mb-3"></i>
                            <h5>40,000+ VOD</h5>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-hd-video fa-3x text-info mb-3"></i>
                            <h5>4K Quality</h5>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-headset fa-3x text-warning mb-3"></i>
                            <h5>24/7 Support</h5>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-home"></i> Go to Website
                </a>
                <button onclick="location.reload()" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-redo"></i> Refresh Test
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test if Bootstrap is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Test Bootstrap CSS by checking if Bootstrap classes work
            const testDiv = document.createElement('div');
            testDiv.className = 'container';
            document.body.appendChild(testDiv);

            const styles = window.getComputedStyle(testDiv);
            const hasBootstrap = styles.paddingLeft !== '0px' || styles.paddingRight !== '0px';

            if (hasBootstrap) {
                document.getElementById('bootstrap-test').innerHTML = '✅ Loaded';
                document.getElementById('bootstrap-test').className = 'status-good';
            } else {
                document.getElementById('bootstrap-test').innerHTML = '❌ Not Loaded - Check Console';
                console.error('Bootstrap CSS not loading. Check network tab.');
            }

            document.body.removeChild(testDiv);
            
            // Test custom CSS by checking if a custom class exists
            const testElement = document.createElement('div');
            testElement.className = 'hero-section';
            document.body.appendChild(testElement);
            
            const styles = window.getComputedStyle(testElement);
            if (styles.background && styles.background !== 'rgba(0, 0, 0, 0)') {
                document.getElementById('custom-css-test').innerHTML = '✅ Loaded';
                document.getElementById('custom-css-test').className = 'status-good';
            }
            
            document.body.removeChild(testElement);
            
            // Show current URL info
            console.log('Current URL:', window.location.href);
            console.log('CSS Path:', 'https://badboyzmedia.org/assets/css/style.css');
        });
    </script>
</body>
</html>
