<?php
// BadBoyz IPTV - Header Include
if (!defined('BADBOYZ_IPTV')) {
    define('BADBOYZ_IPTV', true);
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
}

// Track page view
$currentPage = basename($_SERVER['PHP_SELF']);
trackPageView($currentPage);

// Get alert message if any
$alert = getAlert();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME . ' - Premium IPTV Service Provider'; ?></title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : 'Premium IPTV service with 16000+ channels, 40000+ VOD, 4K quality streaming. Best IPTV subscription with 24/7 support and worldwide coverage.'; ?>">
    <meta name="keywords" content="IPTV, streaming, TV channels, VOD, movies, series, 4K, HD, subscription, BadBoyz">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo APP_URL; ?>/assets/images/favicon.ico">
    <link rel="icon" type="image/svg+xml" href="<?php echo APP_URL; ?>/assets/images/favicon.svg">
    
    <!-- CSS Files - Load Bootstrap first -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo APP_URL; ?>/assets/css/style.css?v=<?php echo time(); ?>">

    <!-- Fallback CSS in case Bootstrap doesn't load -->
    <style>
        /* Basic fallback styles */
        .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
        .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
        .col, .col-md-6, .col-lg-4, .col-lg-6, .col-lg-8 { flex: 1; padding: 0 15px; }
        .btn { display: inline-block; padding: 12px 24px; text-decoration: none; border-radius: 5px; border: none; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-lg { padding: 15px 30px; font-size: 18px; }
        .alert { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .text-center { text-align: center; }
        .mb-3 { margin-bottom: 1rem; }
        .py-5 { padding: 3rem 0; }
        @media (max-width: 768px) {
            .row { flex-direction: column; }
            .col, .col-md-6, .col-lg-4, .col-lg-6, .col-lg-8 { width: 100%; }
        }
    </style>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <?php if (isset($site_settings['google_analytics_id']) && $site_settings['analytics_enabled']): ?>
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo $site_settings['google_analytics_id']; ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '<?php echo $site_settings['google_analytics_id']; ?>');
    </script>
    <?php endif; ?>
    
    <?php if (isset($site_settings['facebook_pixel_id']) && $site_settings['analytics_enabled']): ?>
    <!-- Facebook Pixel -->
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '<?php echo $site_settings['facebook_pixel_id']; ?>');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=<?php echo $site_settings['facebook_pixel_id']; ?>&ev=PageView&noscript=1"/></noscript>
    <?php endif; ?>
    
    <!-- CSRF Token for AJAX requests -->
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
</head>
<body>
    <?php if ($site_settings['maintenance_mode'] && !isAdmin()): ?>
    <div class="maintenance-banner">
        <div class="container text-center py-3">
            <i class="fas fa-tools"></i> Website is under maintenance. We'll be back soon!
        </div>
    </div>
    <?php endif; ?>

    <?php if ($alert): ?>
    <div class="alert alert-<?php echo $alert['type']; ?> alert-dismissible fade show m-0" role="alert">
        <div class="container">
            <?php echo htmlspecialchars($alert['message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
    <?php endif; ?>

    <!-- Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container">
                <a class="navbar-brand" href="index.php">
                    <span class="logo-text"><?php echo APP_NAME; ?></span>
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage == 'index.php') ? 'active' : ''; ?>" href="index.php">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage == 'subscriptions.php') ? 'active' : ''; ?>" href="subscriptions.php">IPTV Subscription</a>
                        </li>
                        <?php if ($site_settings['trial_enabled']): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage == 'free-trial.php') ? 'active' : ''; ?>" href="free-trial.php">Free Trial</a>
                        </li>
                        <?php endif; ?>
                        <?php if ($site_settings['reseller_program']): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage == 'reseller.php') ? 'active' : ''; ?>" href="reseller.php">IPTV Reseller</a>
                        </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage == 'tutorials.php') ? 'active' : ''; ?>" href="tutorials.php">Installation Tutorial</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage == 'about.php') ? 'active' : ''; ?>" href="about.php">About Us</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($currentPage == 'contact.php') ? 'active' : ''; ?>" href="contact.php">Contact Us</a>
                        </li>
                        
                        <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION['username']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="client-area.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                                <?php if (isAdmin()): ?>
                                <li><a class="dropdown-item" href="admin/index.php"><i class="fas fa-cog"></i> Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                            </ul>
                        </li>
                        <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link btn btn-outline-primary ms-2" href="login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-primary ms-2" href="register.php">Register</a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
