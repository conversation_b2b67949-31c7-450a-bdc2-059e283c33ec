<?php
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $action = $_REQUEST['action'] ?? '';
    
    switch ($action) {
        case 'start_session':
            handleStartSession();
            break;
            
        case 'send_message':
            handleSendMessage();
            break;
            
        case 'get_messages':
            handleGetMessages();
            break;
            
        case 'log_feedback':
            handleLogFeedback();
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function handleStartSession() {
    global $pdo;
    
    $page = $_POST['page'] ?? '';
    $sessionId = 'chat_' . time() . '_' . bin2hex(random_bytes(8));
    $visitorId = 'visitor_' . time() . '_' . bin2hex(random_bytes(4));
    
    // Create chat session record
    $stmt = $pdo->prepare("
        INSERT INTO chat_sessions (session_id, visitor_id, page_url, created_at) 
        VALUES (?, ?, ?, NOW())
    ");
    
    try {
        $stmt->execute([$sessionId, $visitorId, $page]);
        
        echo json_encode([
            'success' => true,
            'session_id' => $sessionId,
            'visitor_id' => $visitorId
        ]);
    } catch (PDOException $e) {
        // If table doesn't exist, create it
        createChatTables();
        $stmt->execute([$sessionId, $visitorId, $page]);
        
        echo json_encode([
            'success' => true,
            'session_id' => $sessionId,
            'visitor_id' => $visitorId
        ]);
    }
}

function handleSendMessage() {
    global $pdo;
    
    $sessionId = $_POST['session_id'] ?? '';
    $message = $_POST['message'] ?? '';
    $sender = $_POST['sender'] ?? 'customer';
    
    if (empty($sessionId) || empty($message)) {
        throw new Exception('Missing required parameters');
    }
    
    $stmt = $pdo->prepare("
        INSERT INTO chat_messages (session_id, message, sender, created_at) 
        VALUES (?, ?, ?, NOW())
    ");
    
    $stmt->execute([$sessionId, $message, $sender]);
    
    echo json_encode([
        'success' => true,
        'message_id' => $pdo->lastInsertId()
    ]);
}

function handleGetMessages() {
    global $pdo;
    
    $sessionId = $_GET['session_id'] ?? '';
    
    if (empty($sessionId)) {
        throw new Exception('Missing session ID');
    }
    
    $stmt = $pdo->prepare("
        SELECT id, message, sender, created_at,
               DATE_FORMAT(created_at, '%H:%i') as time_display
        FROM chat_messages 
        WHERE session_id = ? 
        ORDER BY created_at ASC
    ");
    
    $stmt->execute([$sessionId]);
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'messages' => $messages
    ]);
}

function handleLogFeedback() {
    global $pdo;
    
    $sessionId = $_POST['session_id'] ?? '';
    $messageId = $_POST['message_id'] ?? '';
    $isPositive = $_POST['is_positive'] ?? '';
    
    if (empty($sessionId) || empty($messageId)) {
        throw new Exception('Missing required parameters');
    }
    
    $stmt = $pdo->prepare("
        INSERT INTO chat_feedback (session_id, message_id, is_positive, created_at) 
        VALUES (?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE 
        is_positive = VALUES(is_positive), 
        created_at = NOW()
    ");
    
    $stmt->execute([$sessionId, $messageId, $isPositive === 'true' ? 1 : 0]);
    
    echo json_encode([
        'success' => true,
        'feedback_logged' => true
    ]);
}

function createChatTables() {
    global $pdo;
    
    // Create chat sessions table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(100) UNIQUE NOT NULL,
            visitor_id VARCHAR(100) NOT NULL,
            page_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_session_id (session_id)
        )
    ");
    
    // Create chat messages table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(100) NOT NULL,
            message TEXT NOT NULL,
            sender ENUM('customer', 'admin', 'bot') NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_session_id (session_id),
            INDEX idx_created_at (created_at)
        )
    ");
    
    // Create chat feedback table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS chat_feedback (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(100) NOT NULL,
            message_id VARCHAR(100) NOT NULL,
            is_positive BOOLEAN NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_feedback (session_id, message_id),
            INDEX idx_session_id (session_id),
            INDEX idx_created_at (created_at)
        )
    ");
}
?>
