<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// Verify CSRF token if provided
if (isset($_GET['token']) && !verifyCSRFToken($_GET['token'])) {
    showAlert('Invalid security token.', 'danger');
    redirect('index.php');
}

// Log the logout action
if (isLoggedIn()) {
    $userId = $_SESSION['user_id'];
    $username = $_SESSION['username'];
    
    // You could log this to a user activity table if needed
    error_log("User logout: {$username} (ID: {$userId}) at " . date('Y-m-d H:i:s'));
}

// Perform logout
logoutUser();

// Clear remember me cookie if it exists
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/', '', true, true);
}

showAlert('You have been successfully logged out.', 'success');
redirect('index.php');
?>
