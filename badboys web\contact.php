<?php
$pageTitle = "Contact Us - 24/7 Support";
$pageDescription = "Get in touch with BadBoyz IPTV support team. 24/7 customer service, live chat, email support, and comprehensive FAQ section.";

// Handle contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'config/config.php';
    require_once 'config/database.php';
    require_once 'includes/functions.php';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showAlert('Invalid security token. Please try again.', 'danger');
    } else {
        $name = sanitizeInput($_POST['name'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $subject = sanitizeInput($_POST['subject'] ?? '');
        $message = sanitizeInput($_POST['message'] ?? '');
        $priority = sanitizeInput($_POST['priority'] ?? 'medium');
        
        // Validation
        $errors = [];
        
        if (empty($name)) $errors[] = 'Name is required';
        if (empty($email)) $errors[] = 'Email is required';
        if (empty($subject)) $errors[] = 'Subject is required';
        if (empty($message)) $errors[] = 'Message is required';
        
        if (!validateEmail($email)) {
            $errors[] = 'Please enter a valid email address';
        }
        
        if (empty($errors)) {
            // Create support ticket
            $ticketData = [
                'user_id' => isLoggedIn() ? $_SESSION['user_id'] : null,
                'name' => $name,
                'email' => $email,
                'subject' => $subject,
                'message' => $message,
                'priority' => $priority,
                'status' => 'open'
            ];
            
            $ticketId = insertRecord('support_tickets', $ticketData);
            
            if ($ticketId) {
                // Send confirmation email to user
                $userSubject = 'Support Ticket Created - Ticket #' . $ticketId;
                $userMessage = "
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #007bff;'>Support Ticket Created</h2>
                        <p>Dear {$name},</p>
                        <p>Thank you for contacting " . APP_NAME . ". Your support ticket has been created successfully.</p>
                        
                        <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                            <h3>Ticket Details:</h3>
                            <p><strong>Ticket ID:</strong> #{$ticketId}</p>
                            <p><strong>Subject:</strong> {$subject}</p>
                            <p><strong>Priority:</strong> " . ucfirst($priority) . "</p>
                            <p><strong>Status:</strong> Open</p>
                        </div>
                        
                        <p>Our support team will review your request and respond within 24 hours. You can track your ticket status in your client area.</p>
                        
                        <p>If you have any urgent issues, please contact us via live chat or email at " . SUPPORT_EMAIL . "</p>
                        
                        <p>Best regards,<br>The " . APP_NAME . " Support Team</p>
                    </div>
                </body>
                </html>";
                
                sendEmail($email, $userSubject, $userMessage);
                
                // Send notification to admin
                $adminSubject = 'New Support Ticket #' . $ticketId . ' - ' . $subject;
                $adminMessage = "
                New support ticket created:
                
                Ticket ID: #{$ticketId}
                Name: {$name}
                Email: {$email}
                Subject: {$subject}
                Priority: {$priority}
                
                Message:
                {$message}
                
                View ticket: " . APP_URL . "/admin/index.php?section=tickets&ticket={$ticketId}";
                
                sendEmail(ADMIN_EMAIL, $adminSubject, $adminMessage, false);
                
                showAlert('Your message has been sent successfully! We will respond within 24 hours.', 'success');
                
                // Clear form data
                unset($_POST);
            } else {
                showAlert('Failed to send message. Please try again.', 'danger');
            }
        } else {
            foreach ($errors as $error) {
                showAlert($error, 'danger');
                break; // Show only first error
            }
        }
    }
}

require_once 'includes/header.php';
?>

    <!-- Hero Section -->
    <section class="contact-hero py-5">
        <div class="container">
            <div class="row text-center">
                <div class="col-12">
                    <h1 class="hero-title">Contact Our Support Team</h1>
                    <p class="hero-subtitle">We're here to help you 24/7. Get in touch with us through any of the methods below.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Methods -->
    <section class="contact-methods py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h4>Live Chat</h4>
                        <p>Get instant help from our support team</p>
                        <button class="btn btn-primary" onclick="toggleChat()">
                            <i class="fas fa-comment-dots"></i> Start Chat
                        </button>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h4>Email Support</h4>
                        <p>Send us a detailed message</p>
                        <a href="mailto:<?php echo SUPPORT_EMAIL; ?>" class="btn btn-primary">
                            <i class="fas fa-envelope"></i> Send Email
                        </a>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fab fa-telegram"></i>
                        </div>
                        <h4>Telegram</h4>
                        <p>Join our Telegram support channel</p>
                        <a href="https://t.me/badboyziptv" class="btn btn-primary" target="_blank">
                            <i class="fab fa-telegram"></i> Join Channel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form -->
    <section class="contact-form-section py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="contact-form-card">
                        <div class="form-header text-center mb-4">
                            <h2>Send us a Message</h2>
                            <p>Fill out the form below and we'll get back to you within 24 hours</p>
                        </div>
                        
                        <form method="POST" class="contact-form">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required
                                           value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email" required
                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <input type="text" class="form-control" id="subject" name="subject" required
                                           value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="priority" class="form-label">Priority</label>
                                    <select class="form-control" id="priority" name="priority">
                                        <option value="low" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'low') ? 'selected' : ''; ?>>Low</option>
                                        <option value="medium" <?php echo (!isset($_POST['priority']) || $_POST['priority'] == 'medium') ? 'selected' : ''; ?>>Medium</option>
                                        <option value="high" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'high') ? 'selected' : ''; ?>>High</option>
                                        <option value="urgent" <?php echo (isset($_POST['priority']) && $_POST['priority'] == 'urgent') ? 'selected' : ''; ?>>Urgent</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">Message *</label>
                                <textarea class="form-control" id="message" name="message" rows="6" required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-paper-plane"></i> Send Message
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">Frequently Asked Questions</h2>
                    <p class="section-subtitle">Find quick answers to common questions</p>
                </div>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="faq-item">
                        <div class="faq-question">
                            <h5>How quickly will I receive my subscription details?</h5>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>You will receive your subscription details via email within 5-10 minutes after successful payment.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question">
                            <h5>What devices are supported?</h5>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Our IPTV service works on Smart TVs, Android devices, iOS devices, MAG boxes, Fire Stick, PC, Mac, and any device that supports M3U playlists.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question">
                            <h5>Do you offer refunds?</h5>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Yes, we offer a 7-day money-back guarantee. If you're not satisfied with our service, contact us within 7 days for a full refund.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question">
                            <h5>How many devices can I use simultaneously?</h5>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>This depends on your subscription plan. Basic plan allows 2 connections, Pro plan allows 3 connections, and Premium plan allows 4 connections.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question">
                            <h5>Is there a setup fee?</h5>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p>No, there are no setup fees. You only pay the monthly subscription fee for your chosen plan.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
.contact-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.contact-card {
    text-align: center;
    padding: 40px 30px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    height: 100%;
    transition: transform 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
}

.contact-form-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.faq-item {
    background: white;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.faq-question {
    padding: 20px;
    cursor: pointer;
    display: flex;
    justify-content: between;
    align-items: center;
    background: #f8f9fa;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: #e9ecef;
}

.faq-question h5 {
    margin: 0;
    flex: 1;
}

.faq-question i {
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 20px;
    max-height: 200px;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .contact-card {
        margin-bottom: 30px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
