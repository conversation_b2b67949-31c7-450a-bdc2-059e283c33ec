<?php
// BadBoyz IPTV - Core Functions
if (!defined('BADBOYZ_IPTV')) {
    die('Direct access not permitted');
}

// Security Functions
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

// User Authentication Functions
function loginUser($email, $password) {
    $user = fetchOne("SELECT * FROM users WHERE email = ? AND status = 'active'", [$email]);
    
    if (!$user) {
        return ['success' => false, 'message' => 'Invalid email or password'];
    }
    
    // Check if account is locked
    if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
        return ['success' => false, 'message' => 'Account temporarily locked. Try again later.'];
    }
    
    if (!password_verify($password, $user['password'])) {
        // Increment login attempts
        $attempts = $user['login_attempts'] + 1;
        $lockUntil = null;
        
        if ($attempts >= MAX_LOGIN_ATTEMPTS) {
            $lockUntil = date('Y-m-d H:i:s', time() + LOGIN_LOCKOUT_TIME);
        }
        
        updateRecord('users', 
            ['login_attempts' => $attempts, 'locked_until' => $lockUntil], 
            'id = ?', [$user['id']]
        );
        
        return ['success' => false, 'message' => 'Invalid email or password'];
    }
    
    // Reset login attempts and update last login
    updateRecord('users', 
        ['login_attempts' => 0, 'locked_until' => null, 'last_login' => date('Y-m-d H:i:s')], 
        'id = ?', [$user['id']]
    );
    
    // Set session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_type'] = $user['user_type'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['email'] = $user['email'];
    
    return ['success' => true, 'user' => $user];
}

function logoutUser() {
    session_destroy();
    session_start();
    generateCSRFToken(); // Generate new token
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'admin';
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

function requireAdmin() {
    if (!isAdmin()) {
        header('Location: index.php');
        exit;
    }
}

// Email Functions (Simple mail function - replace with PHPMailer for production)
function sendEmail($to, $subject, $message, $isHTML = true) {
    $headers = "From: " . APP_NAME . " <" . SMTP_USERNAME . ">\r\n";
    $headers .= "Reply-To: " . SUPPORT_EMAIL . "\r\n";

    if ($isHTML) {
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    } else {
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    }
    

    try {
        return mail($to, $subject, $message, $headers);
    } catch (Exception $e) {
        error_log("Email sending failed: " . $e->getMessage());
        return false;
    }
}

// Subscription Functions
function createSubscription($userId, $planType, $paymentMethod = 'paypal', $transactionId = null) {
    global $subscription_plans;
    
    if (!isset($subscription_plans[$planType])) {
        return false;
    }
    
    $plan = $subscription_plans[$planType];
    $startDate = date('Y-m-d');
    $endDate = date('Y-m-d', strtotime('+1 month'));
    
    // Generate IPTV credentials
    $username = 'bb_' . generateRandomString(8);
    $password = generateRandomString(12);
    $serverUrl = 'http://server.badboyz-iptv.com:8080';
    
    $subscriptionData = [
        'user_id' => $userId,
        'plan_type' => $planType,
        'connections' => $plan['connections'],
        'price' => $plan['price'],
        'start_date' => $startDate,
        'end_date' => $endDate,
        'payment_method' => $paymentMethod,
        'transaction_id' => $transactionId,
        'server_url' => $serverUrl,
        'username' => $username,
        'password' => $password,
        'status' => 'active'
    ];
    
    return insertRecord('subscriptions', $subscriptionData);
}

function createTrial($email, $deviceInfo = '') {
    global $site_settings;
    
    if (!$site_settings['trial_enabled']) {
        return ['success' => false, 'message' => 'Trials are currently disabled'];
    }
    
    $ipAddress = $_SERVER['REMOTE_ADDR'];
    
    // Check if IP already used trial
    $existingTrial = fetchOne("SELECT id FROM trials WHERE ip_address = ?", [$ipAddress]);
    if ($existingTrial && $site_settings['max_trials_per_ip'] <= 1) {
        return ['success' => false, 'message' => 'Trial already used from this IP address'];
    }
    
    // Generate trial credentials
    $username = 'trial_' . generateRandomString(8);
    $password = generateRandomString(10);
    $serverUrl = 'http://trial.badboyz-iptv.com:8080';
    $endTime = date('Y-m-d H:i:s', time() + ($site_settings['trial_duration'] * 3600));
    
    $trialData = [
        'email' => $email,
        'ip_address' => $ipAddress,
        'device_info' => $deviceInfo,
        'username' => $username,
        'password' => $password,
        'server_url' => $serverUrl,
        'end_time' => $endTime,
        'status' => 'active'
    ];
    
    $trialId = insertRecord('trials', $trialData);
    
    if ($trialId) {
        // Send trial email
        $subject = 'Your 24-Hour IPTV Trial - BadBoyz IPTV';
        $message = getTrialEmailTemplate($username, $password, $serverUrl, $endTime);
        sendEmail($email, $subject, $message);
        
        return ['success' => true, 'trial_id' => $trialId, 'credentials' => $trialData];
    }
    
    return ['success' => false, 'message' => 'Failed to create trial'];
}

// Template Functions
function getTrialEmailTemplate($username, $password, $serverUrl, $endTime) {
    return "
    <html>
    <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
        <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
            <h2 style='color: #007bff;'>Your 24-Hour IPTV Trial is Ready!</h2>
            <p>Thank you for choosing BadBoyz IPTV. Your trial account has been created successfully.</p>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                <h3>Your Trial Credentials:</h3>
                <p><strong>Server URL:</strong> {$serverUrl}</p>
                <p><strong>Username:</strong> {$username}</p>
                <p><strong>Password:</strong> {$password}</p>
                <p><strong>Expires:</strong> " . date('M j, Y g:i A', strtotime($endTime)) . "</p>
            </div>
            
            <h3>How to Use:</h3>
            <ol>
                <li>Download an IPTV player (VLC, Perfect Player, IPTV Smarters, etc.)</li>
                <li>Enter the credentials above</li>
                <li>Start streaming 16,000+ channels and 40,000+ VOD content</li>
            </ol>
            
            <p>Need help? Contact our support team at " . SUPPORT_EMAIL . "</p>
            
            <p style='margin-top: 30px;'>
                <a href='" . APP_URL . "/subscriptions.php' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;'>Subscribe Now</a>
            </p>
        </div>
    </body>
    </html>";
}

// Utility Functions
function redirect($url) {
    header("Location: $url");
    exit;
}

function showAlert($message, $type = 'info') {
    $_SESSION['alert'] = ['message' => $message, 'type' => $type];
}

function getAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        return $alert;
    }
    return null;
}

function formatPrice($price) {
    return '$' . number_format($price, 2);
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    
    return floor($time/31536000) . ' years ago';
}

// Analytics Functions
function trackPageView($pageUrl) {
    global $site_settings;
    
    if (!$site_settings['analytics_enabled']) {
        return;
    }
    
    $analyticsData = [
        'page_url' => $pageUrl,
        'visitor_ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'referrer' => $_SERVER['HTTP_REFERER'] ?? '',
        'visit_date' => date('Y-m-d'),
        'visit_time' => date('H:i:s'),
        'session_id' => session_id(),
        'user_id' => $_SESSION['user_id'] ?? null
    ];
    
    insertRecord('site_analytics', $analyticsData);
}
?>
